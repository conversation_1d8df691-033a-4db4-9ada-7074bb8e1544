#! /bin/shell

#======================================================================
# 项目停服shell脚本
# 通过项目名称查找到PID
# 然后kill -9 pid
#
#======================================================================

# 项目名称
APPLICATION="dc-iceage-pe"

# 项目启动jar包名称
APPLICATION_JAR="${APPLICATION}.jar"

PID=$(ps -ef | grep "${APPLICATION_JAR}" | grep -v grep | awk '{ print $2 }')
if [[ -z "$PID" ]]
then
    echo ${APPLICATION} is already stopped
else
    echo run command '['kill -2 ${PID}']'
    kill -2 ${PID}
    echo closing ...
    sleep 10
    PID=$(ps -ef | grep "${APPLICATION_JAR}" | grep -v grep | awk '{ print $2 }')
    if [[ "$PID" ]]
    then
      echo closing failed
      echo run command '['kill -9 ${PID}']'
      kill -9 ${PID}
    fi
    echo ${APPLICATION} stopped successfully
fi
