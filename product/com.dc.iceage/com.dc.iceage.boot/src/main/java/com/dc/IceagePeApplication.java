package com.dc;

import com.dc.iceage.config.IceageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

@Slf4j
@SpringBootApplication
@ComponentScan({"com.dc", "com.pingan"})
@EnableConfigurationProperties(IceageConfig.class)
public class IceagePeApplication {

    public static void main(String[] args) {
        SpringApplication.run(IceagePeApplication.class, args);
        log.info("Iceage PE 已启动！");
    }

}
