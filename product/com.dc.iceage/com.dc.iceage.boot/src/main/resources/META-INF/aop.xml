<aspectj>
    <weaver options="">
        <!-- 哪些类需要被织入 -->
        <include within="com.dc.springboot.core.model.result.WebSQLParserResult"/>
        <include within="com.dc.springboot.core.component.WebSQLParserResultSetterMonitorAspect"/>

        <!-- 可以排除 Spring 框架类或其他你不需要织入的类 -->
        <exclude within="org.springframework.*"/>
        <exclude within="java.*"/>
        <exclude within="javax.*"/>
    </weaver>

    <!-- 哪些 Aspect 需要被应用 -->
    <aspects>
        <aspect name="com.dc.springboot.core.component.WebSQLParserResultSetterMonitorAspect"/>
        <!-- 如果你有其他 AspectJ 切面，也在这里列出 -->
    </aspects>
</aspectj>