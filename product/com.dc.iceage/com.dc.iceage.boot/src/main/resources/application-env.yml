server:
  port: ${dc_iceage_port}     # 访问端口号
  servlet:
    context-path: /dc-iceage  # 访问路径，不建议修改
  tomcat:
    max-http-form-post-size: 20MB
  shutdown: graceful
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,application/javascript,application/json,image/svg+xml
    min-response-size: 1024

iceage:
  global:
    root: ${global_root_path} # 根目录：用来存放驱动包、临时文件等
    krb5: ${global_krb5_path} # Kerberos文件：hive存放krb5配置文件
  thread-pool: # 默认线程池配置
    name: default
    core-pool-size: 0       # 核心线程数
    maximum-pool-size: 20   # 最大线程数
    keep-alive-time: 60     # 线程存活时间（秒）
  thread-pools: # 指定线程池配置：如果不填写，则使用上面的默认配置。
    - name: ExecuteThread     # 可选项有：ExecuteThread、RefreshThread
      core-pool-size: 5       # 核心线程数
      maximum-pool-size: 100  # 最大线程数
      keep-alive-time: 1800   # 线程存活时间（秒）
    - name: RefreshThread
      maximum-pool-size: 1
    # - name...               # 可添加多个
    # ...
  sql-batch-thread: ${sql_batch_thread:3}         # 批处理的预检查和执行检查，并行执行的线程数量，最小为1，最大为5
  exec-pool:                                      # 连接池配置
    initial-size: 1                               # 初始连接数
    min-idle: 2                                   # 最小连接池数量
    max-active: 20                                # 最大连接池数量
    max-wait: 60000                               # 配置获取连接等待超时的时间
    time-between-eviction-runs-millis: 60000     # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
    min-evictable-idle-time-millis: 300000        # 配置一个连接在池中最小生存的时间，单位是毫秒
    max-evictable-idle-time-millis: 600000        # 配置一个连接在池中最大生存的时间，单位是毫秒
    test-while-idle: true                         # 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
    test-on-borrow: true                          # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
    test-on-return: false                         # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
    connect-timeout: 60000                        # 建立链接需要的时间。该参数只在建立链接阶段生效，单位毫秒。
    socket-timeout: 600000                       # 发送请求给数据库（建立链接后），数据库处理的最大时间；超过这个客户端报超时超时异常（Caused by: java.net.SocketTimeoutException: Read timed out），单位毫秒。
  ext-mongo:
    retry-reads: true                             # 启用读操作的自动重试功能。适合偶发网络问题或主从切换场景。
    retry-writes: false                           # 禁用写操作的自动重试功能，避免因幂等性问题导致的数据重复或错误。
    server-selection-timeout-m-s: 60000           # 服务器选择超时时间（毫秒）。客户端尝试连接到合适的 MongoDB 服务器的最大等待时间。
    connect-timeout-m-s: 60000                    # 连接超时时间（毫秒）。客户端与 MongoDB 服务器建立连接时的超时时间。
    read-timeout-m-s: 60000                       # 读操作超时时间（毫秒）。客户端读取数据时的最大等待时间。
    max-size: 20                                  # 最大连接数。连接池中可以同时存在的最大连接数。
    min-size: 2                                   # 最小连接数。连接池中始终保持的最少连接数。
    max-connecting: 1                             # 最大并发连接数。连接池中同时尝试建立的新连接数量限制。
    max-wait-time-m-s: 60000                      # 最大等待时间（毫秒）。连接池中等待可用连接的最大时间，超过则抛出异常。
    max-connection-life-time-m-s: 300000          # 最大连接存活时间（毫秒）。连接池中的连接达到此时间后将被关闭并移除。
    max-connection-idle-time-m-s: 300000          # 最大连接空闲时间（毫秒）。连接池中空闲连接超过此时间将被关闭。
    heartbeat-frequency-m-s: 300000               # 心跳检查频率（毫秒）。客户端与服务器间检查连接状态的时间间隔。
  ext-redis:
    connect-timeout-m-s: 60000                    # Redis连接超时时间（单位：毫秒）。超过这个时间未建立连接则超时。
    socket-timeout-m-s: 60000                     # Redis读写操作的超时时间（单位：毫秒）。超过这个时间未完成操作则超时。
    max-total: 1                                  # Redis连接池的最大连接数。限制并发连接的上限。
    max-idle: 20                                  # Redis连接池中允许的最大空闲连接数。如果连接数超过这个值，多余的空闲连接将被释放。
    min-idle: 2                                   # Redis连接池中保持的最小空闲连接数。即使连接很少，也会确保这个数量的空闲连接。
    max-wait-m-s: 60000                           # 当连接池耗尽时，客户端等待可用连接的最大时间（单位：毫秒）。超过此时间会抛出异常。
    time-between-eviction-runs-m-s: 300000        # 空闲连接回收线程的运行间隔（单位：毫秒）。设置为负数时，不运行空闲连接回收线程。
    min-evictable-idle-time-m-s: 300000           # 连接被视为空闲并可被回收的最短时间（单位：毫秒）。
    soft-min-evictable-idle-m-s: 300000           # 如果连接池中的连接数大于 `min-idle`，空闲连接可被回收的时间（单位：毫秒）。
    test-on-create: false                         # 在创建连接时是否检查连接是否有效。增加安全性，但会影响性能。
    test-on-borrow: true                          # 在从连接池中借用连接时，是否检查连接是否有效。建议设置为 `true`。
    test-on-return: false                         # 在将连接返回到连接池时，是否检查连接是否有效。一般设置为 `false` 以减少额外开销。
    test-while-idle: true                         # 在空闲连接回收线程运行时，是否检查空闲连接是否有效。建议设置为 `true`。
    num-tests-per-eviction-run: -1                # 每次空闲连接回收线程运行时，检查的连接数量。设置为 `-1` 表示检查所有连接。
  session:                    # 会话配置
    maximum-context-size: 500 # 当前数据源最大连接数量
    keep-alive-time: 3600     # 存活时间（秒），如果超过这个时间，将会自动关闭
    query-timeout: 3600       # 查询超时时间（秒），执行 SQL 时
    # network-timeout: 3600     # 网络超时时间（秒），与数据库通信的 socket ，这个值必须大于 query-timeout
    app-name: DC              # 会话窗口显示的应用名称
  data-source:            # 数据源配置
    keep-alive-time: 1800 # 存活时间（秒），如果超过这个时间，将会自动关闭
  path:
    dc-backend: http://${dc_backend_ip}:${dc_backend_port}
    dc-spi: http://${dc_spi_ip}:${dc_spi_port}
  key-prefix: ${dc_key_prefix}
  sql-fingerprint-conf:                                               # SQL指纹配置
    initial-capacity: ${sql_fingerprint_initial_capacity:100}         # 缓存初始容量
    maximum-size: ${sql_fingerprint_maximum_size:1000}                # 缓存最大容量
    expire-after-write: ${sql_fingerprint_expire_after_write:10m}     # 写入后过期时间  数字+单位(d:天,h:小时,m:分钟,s:秒)
    record-stats: ${sql_fingerprint_record_stats:false}               # 记录统计信息 false:关闭,true:开启
    type: ${sql_fingerprint_type:NO_CACHE}                            # NO_CACHE: 无缓存; READ_ACTION: 读取行为

spring:
  lifecycle:
    timeout-per-shutdown-phase: 5s
  redis:
    key-prefix: ${redis_keyPrefix:dc_}        # key 前缀
    read-model: ${redis_read_model:1}         # 读取模式：0，从节点读取；1，主节点读取；2，主从节点读取。
    sentinel-servers-config:                              # 哨兵模式，优先顺序：1 ----------------------------------------------------------------
      sentinel-addresses: ${redis_nodes_list_sentinel:}   # 哨兵地址，如果不为空，那么使用哨兵模式 ！！！
      password: ${redis_password}                         # 密码
      master-name: ${redis_master}                        # 主节点名称
      master-connection-pool-size: 30                     # 主节点连接池最大数量，默认是：64
      master-connection-minimum-idle-size: 5              # 主节点连接池最小空闲，默认是：24
      slave-connection-pool-size: 30                      # 从节点连接池最大数量，默认是：64
      slave-connection-minimum-idle-size: 5               # 从节点连接池最小空闲，默认是：24
      timeout: 30000                                      # Redis服务器响应超时。Redis命令发送成功后开始倒计时。值（以毫秒为单位）。默认是：3000
      connect-timeout: 10000                              # 连接到任何Redis服务器时超时。值（以毫秒为单位）。默认是：10000
      idle-connection-timeout: 10000                      # 如果池连接在超时时间内未使用，并且当前连接数大于最小空闲连接池大小，则它将关闭并从池中删除。值（以毫秒为单位）。默认是：10000
      retry-attempts: ${redis_max_redirects:3}            # 重试次数，默认是：3
      retry-interval: 1500                                # 重试间隔。值（以毫秒为单位）。默认是：1500
      ping-connection-interval: 60000                     # Ping 连接服务器命令的间隔。值（以毫秒为单位）。默认是：30000
    cluster-servers-config:                         # 集群模式，优先顺序：2 ----------------------------------------------------------------
      node-addresses: ${redis_nodes_list_cluster:}  # 集群地址，如果不为空，那么使用集群模式 ！！！
      password: ${redis_password}                   # 密码
      master-connection-pool-size: 30               # 主节点连接池最大数量，默认是：64
      master-connection-minimum-idle-size: 5        # 主节点连接池最小空闲，默认是：24
      slave-connection-pool-size: 30                # 从节点连接池最大数量，默认是：64
      slave-connection-minimum-idle-size: 5         # 从节点连接池最小空闲，默认是：24
      timeout: 30000                                # Redis服务器响应超时。Redis命令发送成功后开始倒计时。值（以毫秒为单位）。默认是：3000
      connect-timeout: 10000                        # 连接到任何Redis服务器时超时。值（以毫秒为单位）。默认是：10000
      idle-connection-timeout: 10000                # 如果池连接在超时时间内未使用，并且当前连接数大于最小空闲连接池大小，则它将关闭并从池中删除。值（以毫秒为单位）。默认是：10000
      retry-attempts: ${redis_max_redirects:3}      # 重试次数，默认是：3
      retry-interval: 1500                          # 重试间隔。值（以毫秒为单位）。默认是：1500
      ping-connection-interval: 60000               # Ping 连接服务器命令的间隔。值（以毫秒为单位）。默认是：30000
    single-server-config:                                       # 单机模式，优先顺序：3 ----------------------------------------------------------------
      address: ${redis_ip}:${redis_port}                        # 单机地址，如果不为空，那么使用单机模式 ！！！
      password: ${redis_password}                               # 密码
      database: ${redis_database:2}                             # 用于Redis连接的数据库索引，默认是：0
      connection-pool-size: 30                                  # 连接池最大数量，默认是：64
      connection-minimum-idle-size: 5                           # 连接池最小空闲，默认是：24
      timeout: 30000                                            # Redis服务器响应超时。Redis命令发送成功后开始倒计时。值（以毫秒为单位）。默认是：3000
      connect-timeout: 10000                                    # 连接到任何Redis服务器时超时。值（以毫秒为单位）。默认是：10000
      idle-connection-timeout: 10000                            # 如果池连接在超时时间内未使用，并且当前连接数大于最小空闲连接池大小，则它将关闭并从池中删除。值（以毫秒为单位）。默认是：10000
      retry-attempts: ${redis_max_redirects:3}                  # 重试次数，默认是：3
      retry-interval: 1500                                      # 重试间隔。值（以毫秒为单位）。默认是：1500
      ping-connection-interval: 60000                           # Ping 连接服务器命令的间隔。值（以毫秒为单位）。默认是：30000
  mysql:
    node-server-config:                      # 节点模式。根据 url 中的 ip 不为空，激活节点。单机，配置 normal 下的信息即可。双主、主从：配置 master 和 standby 下的信息即可。
      - name: master                                                # master: 节点名称，可随意修改。
        url: jdbc:mysql://${mysql_ip_master:}:${mysql_port_master}/${mysql_database_name_master}?useUnicode=true&autoReconnectForPools=true&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true
        username: ${mysql_username_master}                          # 用户名
        password: ${mysql_password_master}                          # 密码
        initialSize: 1                                              # 初始连接数，默认是：0
        minIdle: 5                                                  # 最小连接池数量，默认是：0
        maxActive: 50                                               # 最大连接池数量，默认是：8
        maxWait: 1000                                               # 配置获取连接等待超时的时间
        breakAfterAcquireFailure: true                              # 宕机自动重连
        connectTimeout: 60000                                       # 连接超时时间
        socketTimeout: 3600000                                      # 数据超时时间
      - name: standby                                               # standby: 节点名称，可随意修改。
        url: jdbc:mysql://${mysql_ip_standby:}:${mysql_port_standby}/${mysql_database_name_standby}?useUnicode=true&autoReconnectForPools=true&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true
        username: ${mysql_username_standby}                         # 用户名
        password: ${mysql_password_standby}                         # 密码
        initialSize: 1                                              # 初始连接数，默认是：0
        minIdle: 5                                                  # 最小连接池数量，默认是：0
        maxActive: 50                                               # 最大连接池数量，默认是：8
        maxWait: 1000                                               # 配置获取连接等待超时的时间
        breakAfterAcquireFailure: true                              # 宕机自动重连
        connectTimeout: 60000                                       # 连接超时时间
        socketTimeout: 3600000                                      # 数据超时时间
      - name: normal                                                # normal: 节点名称，可随意修改。
        url: jdbc:mysql://${mysql_ip:}:${mysql_port}/${mysql_database_name}?useUnicode=true&autoReconnectForPools=true&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true
        username: ${mysql_username}                                 # 用户名
        password: ${mysql_password}                                 # 密码
        initialSize: 1                                              # 初始连接数，默认是：0
        minIdle: 5                                                  # 最小连接池数量，默认是：0
        maxActive: 50                                               # 最大连接池数量，默认是：8
        maxWait: 1000                                               # 配置获取连接等待超时的时间
        breakAfterAcquireFailure: true                              # 宕机自动重连
        connectTimeout: 60000                                       # 连接超时时间
        socketTimeout: 3600000                                      # 数据超时时间
  datasource:
    druid:
      stat-view-servlet:                            # 监控配置
        # login-username: admin                     # 登录账号
        # login-password: 123456                    # 登录密码
        reset-enable: false                         # 重置监控页面数据
        url-pattern: /execpool/*                    # 登录页面后缀
        enabled: true                               # 开启监控
        allow:                                      # 添加IP白名单,不写就是所有都允许
      web-stat-filter:                              # 监控配置中的 web监控
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        enabled: true
  servlet:
    multipart:
      max-request-size: 20MB
      max-file-size: 20MB

cmdb:
  path: https://${cmdb_path_ip:}:${cmdb_path_port:}
  app-id: ${cmdb_app_id:}
  access-key-id: ${cmdb_access_key_id:}
  access-key-secret: ${cmdb_access_key_secret:}

auth:
  api-conf:
    secret-key: ${auth_api_conf_secret_key:}                  # 接口验证私钥，内置了默认值，建议使用自定义值。
    access-key: ${auth_api_conf_access_key:}                  # 接口验证公钥，内置了默认值，建议使用自定义值。
    validity-period: ${auth_api_conf_validity_period:}        # 接口验证过期时间，默认30秒。(参数: 10s, 30s, 1m, 3m...)
