select (
           select
               wm_concat (name)
           from
               arcdat.bizholder h
           where
                   a.bizid = h.bizid
             and h.peopletype = 1006
             and h.orgtype in (1, 2)
       ) name

from
    arcdat.bizmain a;

select a.bizid from (select bizid from bizholder) b, arcdat.bizmain a;

select b from (select id+name as b from aaa);

select 'aaa', b from bbb;

select userid,phone,username, concat(password, ' ') || concat(password, ' ') from SCOTT.tab;

select password - 9 from SCOTT.tab;

select a.password || a.age || name from tab a;

SELECT a.* FROM (SELECT cy3.*,cy2.* FROM "CY3",cy2) a;

SELECT a.* FROM (SELECT id as id1, name as name1 FROM "CY3") a;

select count(*) from aaa;

SELECT * FROM (SELECT id as id1, name as name1 FROM "CY3");

SELECT * FROM (SELECT cy3.*,cy2.* FROM "CY3",cy2);

select (
           select
               name
           from
               arcdat.bizholder h
       ) name

from
    arcdat.bizmain a;

SELECT id, name as aaa FROM "CYUSER1"."CY4" union
SELECT id,name as bbb FROM "CYUSER1"."CY5" union
SELECT id as ggg,name as ccc FROM "CYUSER1"."CY6";