insert first when id >= 2 then into test3(id, name) when id >= 3 then into test4(id, name) select t.id, t.password from tab t;
INSERT ALL INTO t1(object_id, object_name) INTO t2(object_id, object_name) SELECT password, name FROM tab;
select (password || age || name) from tab;
select max(password) from tab;
select a.password || a.age || name from tab a;
select userid,phone,username, concat(password, ' ') || concat(password, ' ') from SCOTT.tab;
select password - 9 from tab;
select password - 9 from SCOTT.tab;