
select '123456' as password, name  from tab  where id = 1;
select ('123456') as password, name  from tab  where id = 1;
select max('123456') as password, name  from tab  where id = 1;

select concat(password, id ) as pwd, name  from tab  where id = 1;
select concat(password,'001') ,name from tab  where id = 1;
select position('1' in password) from tab  where id = 1;

select concat(a.password, a.id) as pwd, name  from tab a  where a.id = 1;
select concat(a.password,'001') ,name from tab a  where a.id = 1;
select position('1' in a.password) from tab a  where id = 1;

select  a.`password`, name from `tab` a  where a.id = 1;
select  max(a.`password`), name from `tab` a  where a.id = 1;
select  max(a.`password`)  pwd, name from `tab` a  where a.id = 1;
select  max(a.`password`) as pwd, name from `tab` a  where a.id = 1;
select concat(a.`password`, a.id) as pwd, name  from `tab` a  where a.id = 1;
select concat(a.`password`,'001') ,name from `tab` a  where a.id = 1;
select position('1' in a.`password`) from `tab` a  where a.id = 1;

select  `password`, name from tab a ,tab b  where a.id = 1;
select  max(`password`), name from tab a ,tab b  where a.id = 1;
select  a.`password` , name from tab a ,tab b  where a.id = 1;
select  max(a.`password`), name from tab a ,tab b  where a.id = 1;

select * from (select password as pwd from (select a.password , name from (select *  from tab ) a) b) c;
select a.password , name from (select *  from tab ) a;
select password , name from (select *  from tab ) a;
select * from (select password as pwd from (select a.password , name from (select *  from tab ) a) b) c;
select CONCAT(password, id) from tab;
select c from (select CONCAT(password, id) as c from tab) a;

select CASE WHEN `id` IS NOT NULL THEN  (select password from tab limit 1) ELSE ('001') END from tab;
select substring(password from 1 for 2), name from tab  where  id = 1;
select substring(password,1,2), name from tab  where  id = 1;
select adddate(password,interval 5 day), name from tab  where  id = 1;
select subdate(password,2), name from tab  where  id = 1;
select subdate(password,interval 5 minute), name from tab  where  id = 1;
select adddate(password,2), name from tab  where  id = 1;
select CONCAT_WS((select password from tab),t1,t3) nnn, name from tab  where  id = 1;
select date_format(time, '%Y-%m-%d') from tab;
select position('1' in password) from tab ;
select position('1' in a.password) from tab a;
select id, (name) as temp from tab;
select (select `password` from `tab` LIMIT 1);
select id, (select max(password) from tab) as password from tab2;
select c from (select CONCAT(password, id) as c from tab) a;
select password from tab where name > any(select password from tab);
select password, name  FROM tab WHERE password < ANY(SELECT DISTINCT password FROM tab WHERE department = '管理系') AND department != '管理系';

select date_sub(password,INTERVAL 1 hour) from tab;
select date_sub(password,INTERVAL 1 day) from tab;
select extract(year from password) from tab;
select extract(month from password) from tab;
select extract(day from password) from tab;
select extract(hour from password) from tab;
select extract(minute from password) from tab;


select substring(password,1) from tab;
select substring(password from 1) from tab;
select substring(password,1,6) from tab;
select substring(password from 1 for 6) from tab;
select substring(password,-7) from tab;
select ename,name,CONCAT(name,password) from tab where dept3.deptno=emp3.dept_id and ename='qiaofeng';

select substring(password from 1 for 3) from tab;

select  a.`password` , name from tab a ,tab b  where a.id = 1;
select id, (name)  from tab;
select (select name)  from student;