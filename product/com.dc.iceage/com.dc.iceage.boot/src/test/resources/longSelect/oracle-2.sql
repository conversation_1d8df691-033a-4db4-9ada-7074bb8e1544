select a.bizid,a.bi<PERSON><PERSON>,a.ACCEPTEDTIME,a.COMMITTIME commitdatetime,b.CURRENCY,b.CALPRICETYPE ,b.PAYTYPE,b.FIRSTPAYRATIO,c.RBLDROOMID bldroomid,d.projectname,
       case when a.subtypeid in (60135,9163801) and c.forecastbuildarea>0.1 then c.forecastbuildarea  else c.buildarea end buildarea,b.zxdj NUMBERVALUE5,b.zxzj NUMBERVALUE6,(nvl(b.amount,0)+nvl(b.zxzj,0)) fwzj,
       d.street,d.doorno,c.structure,c.forecastsetarea usearea,c.bargainvalue,c.price,c1.bargainvalue selltotalprices,c1.price  sellunitprice,
       b.amount NUMBERVALUE1,b.PRICE NUMBERVALUE2,c.DESIGNUSAGE,a.ACCEPTEDTIME,a.COMMITTIME,c.location,a.subtypeid, t.typename,a.maintypeid,d.decoration repairlevel,
       case when (d.divisionid='450103' or d.divisionid='1' or d.divisionid='0' or d.divisionid is null  )   then '青秀区'
            when (d.divisionid='450102'  or d.divisionid='2' )                   then '兴宁区'
            when (d.divisionid='450105'  or d.divisionid='3'  or d.divisionid='101')                     then '江南区'
            when (d.divisionid='450108'  or d.divisionid='450100')                then '良庆区'
            when d.divisionid='450109'                                                       then '邕宁区'
            when (d.divisionid='450107'   or d.divisionid='6' or d.divisionid='103')                     then '西乡塘区' end district,
       (select wm_concat(name)
        from arcdat.bizholder h
        where a.bizid=h.bizid
          and h.peopletype=1006
          and h.orgtype in(1,2)
       ) name,
       (select censusaddress
        from arcdat.bizholder h
        where a.bizid=h.bizid
          and h.peopletype=1006
          and h.orgtype in(1,2)
          and rownum=1) censusaddress
from arcdat.bizmain a,
     arcdat.onlinecontract b,
     arcdat.bizroom c,
     arcdat.rbldroom c1,
     arcdat.rbuilding d,
     arcdat.bizcategory t
where a.bizid=b.bizid
  and b.bizid=c.bizid
  and c.rbuildid=d.rbuildid
  and c.rbldroomid=c1.rbldroomid
  and a.subtypeid in (60135,9163801,60136,91648,9164801)  -----9163801原来08版网签的业务类别
  and to_char(a.COMMITTIME,'yyyy-mm') between '2022-06' and  '2023-05'    --- <='2023-05'
  and a.subtypeid=t.categoryid
  and d.RBUILDID not in(10024738,10054090,10054091,60012214,60020031,60226457,60527623,60754023,60754043,60754063,60772323,60844903,60844997,60852152,60898852,60900023,60937423,61087316,61091203,61091216,61091221,
                        61091463,61111863,61134766,61155304,61192879,61245085,61245112) ---传晟的测试楼栋
  and ((d.projectname not like '%传晟%' and d.projectname not like '%测试%') or d.projectname is null)
  and a.divisionid in (450100, 0, 1, 450107, 450102, 450109, 450105, 103, 450103, 450000, 450108, 101)
  and d.projectname like '%瀚林%时代%广场%'
  and  a.bizid  not  in (select bizid from arcdat.bizholder h where peopletype=1006 and name like '%住房和城乡%')  ----剔除保全签约项目
  and  c.designusage in('商铺','商业用房','商场','商业','商务中心','商业服务')
    ---and c.designusage in('别墅','公寓','住宅','高档公寓','货币补贴房','拆迁安置房','限价住房','经济适用住房','经济适用房','综合(含住宅)')
  and a.bizid not in (select bizid from arcdat.biz_right t
                      where t.cancelbizid in (select bizid from arcdat.bizmain
                                              where subtypeid=60137
                                                and to_char(COMMITTIME,'yyyy-mm') <='2023-05' /*撤销签约时间*/))----排除撤销网签的业务