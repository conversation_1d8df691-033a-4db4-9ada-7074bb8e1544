select combination_primary,IFNULL(send_quantity,'') send_quantity
from
    (
        select '销售_C端_苏州仓_官方商城渠道'    as combination_primary,'1_1_1_001' as serial_number
        union all select '销售_C端_苏州仓_自营天猫渠道'    as combination_primary,'1_1_1_002' as serial_number
        union all select '销售_C端_苏州仓_天猫超市渠道'    as combination_primary,'1_1_1_003' as serial_number
        union all select '销售_C端_苏州仓_小红书渠道'      as combination_primary,'1_1_1_004' as serial_number
        union all select '销售_C端_苏州仓_网络分销渠道'    as combination_primary,'1_1_1_005' as serial_number
        union all select '销售_C端_苏州仓_京东渠道'        as combination_primary,'1_1_1_006' as serial_number
        union all select '销售_C端_苏州仓_自营拼多多渠道'  as combination_primary,'1_1_1_007' as serial_number
        union all select '销售_C端_苏州仓_考拉渠道'        as combination_primary,'1_1_1_008' as serial_number
        union all select '销售_C端_苏州仓_苏宁渠道'        as combination_primary,'1_1_1_009' as serial_number
        union all select '销售_C端_苏州仓_凯叔优选渠道'    as combination_primary,'1_1_1_010' as serial_number
        union all select '销售_C端_苏州仓_客服权益'        as combination_primary,'1_1_1_011' as serial_number
        union all select '销售_C端_苏州仓_抖音旗舰店渠道'  as combination_primary,'1_1_1_012' as serial_number
        union all select '销售_C端_苏州仓_富集商城渠道'    as combination_primary,'1_1_1_013' as serial_number
        union all select '销售_C端_苏州仓_腾讯惠聚渠道'    as combination_primary,'1_1_1_014' as serial_number
        union all select '销售_C端_苏州仓_微信小商店渠道'  as combination_primary,'1_1_1_015' as serial_number
        union all select '销售_C端_苏州仓_苏宁天猫渠道'    as combination_primary,'1_1_1_016' as serial_number
        union all select '销售_C端_苏州仓_拼多多分销'      as combination_primary,'1_1_1_024' as serial_number
        union all select '销售_C端_苏州仓_唯品会分销'      as combination_primary,'1_1_1_025' as serial_number
        union all select '销售_C端_苏州仓_京东健康渠道'    as combination_primary,'1_1_1_111' as serial_number

        union all select '销售_C端_广州仓_自营天猫渠道'    as combination_primary,'1_1_2_002' as serial_number
        union all select '销售_C端_广州仓_天猫超市渠道'    as combination_primary,'1_1_2_003' as serial_number
        union all select '销售_C端_广州仓_小红书渠道'      as combination_primary,'1_1_2_004' as serial_number
        union all select '销售_C端_广州仓_网络分销渠道'    as combination_primary,'1_1_2_005' as serial_number
        union all select '销售_C端_广州仓_京东渠道'        as combination_primary,'1_1_2_006' as serial_number
        union all select '销售_C端_广州仓_自营拼多多渠道'  as combination_primary,'1_1_2_007' as serial_number
        union all select '销售_C端_广州仓_苏宁渠道'        as combination_primary,'1_1_2_009' as serial_number
        union all select '销售_C端_广州仓_抖音旗舰店渠道'  as combination_primary,'1_1_2_012' as serial_number
        union all select '销售_C端_广州仓_富集商城渠道'    as combination_primary,'1_1_2_013' as serial_number
        union all select '销售_C端_广州仓_拼多多分销'      as combination_primary,'1_1_2_024' as serial_number
        union all select '销售_C端_广州仓_唯品会分销'      as combination_primary,'1_1_2_025' as serial_number

        union all select '销售_C端_苏州电商A仓_成人粉抖音渠道'      as combination_primary,'1_1_3_017' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉猫超渠道'      as combination_primary,'1_1_3_018' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉京东渠道'      as combination_primary,'1_1_3_019' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉天猫渠道'      as combination_primary,'1_1_3_022' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉京东POP'       as combination_primary,'1_1_3_026' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉唯品MP'        as combination_primary,'1_1_3_027' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉淘特渠道'      as combination_primary,'1_1_3_028' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉-天猫爱本官旗' as combination_primary,'1_1_3_031' as serial_number
        union all select '销售_C端_苏州电商A仓_成人粉-微信小程序'   as combination_primary,'1_1_3_032' as serial_number

        union all select '销售_C端_广州电商A仓_成人粉抖音渠道'      as combination_primary,'1_1_4_017' as serial_number
        union all select '销售_C端_广州电商A仓_成人粉天猫渠道'      as combination_primary,'1_1_4_022' as serial_number
        union all select '销售_C端_广州电商A仓_成人粉京东POP'       as combination_primary,'1_1_4_026' as serial_number
        union all select '销售_C端_广州电商A仓_成人粉唯品MP'        as combination_primary,'1_1_4_027' as serial_number
        union all select '销售_C端_广州电商A仓_成人粉-天猫爱本官旗' as combination_primary,'1_1_4_031' as serial_number

        union all select '销售_B端_苏州仓_天猫超市渠道'      as combination_primary,'1_2_1_003' as serial_number
        union all select '销售_B端_苏州仓_京东渠道'          as combination_primary,'1_2_1_006' as serial_number
        union all select '销售_B端_苏州仓_考拉渠道'          as combination_primary,'1_2_1_008' as serial_number
        union all select '销售_B端_苏州仓_苏宁渠道'          as combination_primary,'1_2_1_009' as serial_number
        union all select '销售_B端_苏州仓_成人粉-饿了么渠道' as combination_primary,'1_2_1_030' as serial_number
        union all select '销售_B端_苏州仓_京东健康渠道'      as combination_primary,'1_2_1_111' as serial_number

        union all select '销售_B端_广州仓_天猫超市渠道' as combination_primary,'1_2_2_003' as serial_number
        union all select '销售_B端_广州仓_京东渠道'     as combination_primary,'1_2_2_006' as serial_number
        union all select '销售_B端_广州仓_考拉渠道'     as combination_primary,'1_2_2_008' as serial_number
        union all select '销售_B端_广州仓_苏宁渠道'     as combination_primary,'1_2_2_009' as serial_number
        union all select '销售_B端_广州仓_京东健康渠道' as combination_primary,'1_2_2_111' as serial_number

        union all select '销售_B端_苏州电商A仓_成人粉猫超渠道'    as combination_primary,'1_2_3_018' as serial_number
        union all select '销售_B端_苏州电商A仓_成人粉京东渠道'    as combination_primary,'1_2_3_019' as serial_number
        union all select '销售_B端_苏州电商A仓_成人粉分销'        as combination_primary,'1_2_3_023' as serial_number
        union all select '销售_B端_苏州电商A仓_成人粉-饿了么渠道' as combination_primary,'1_2_3_030' as serial_number

        union all select '销售_B端_广州电商A仓_成人粉猫超渠道'    as combination_primary,'1_2_4_018' as serial_number
        union all select '销售_B端_广州电商A仓_成人粉天猫渠道'    as combination_primary,'1_2_4_022' as serial_number

        union all select '退货_C端_苏州仓_官方商城渠道'    as combination_primary,'2_1_1_001' as serial_number
        union all select '退货_C端_苏州仓_自营天猫渠道'    as combination_primary,'2_1_1_002' as serial_number
        union all select '退货_C端_苏州仓_天猫超市渠道'    as combination_primary,'2_1_1_003' as serial_number
        union all select '退货_C端_苏州仓_小红书渠道'      as combination_primary,'2_1_1_004' as serial_number
        union all select '退货_C端_苏州仓_网络分销'        as combination_primary,'2_1_1_005' as serial_number
        union all select '退货_C端_苏州仓_京东渠道'        as combination_primary,'2_1_1_006' as serial_number
        union all select '退货_C端_苏州仓_自营拼多多渠道'  as combination_primary,'2_1_1_007' as serial_number
        union all select '退货_C端_苏州仓_考拉渠道'        as combination_primary,'2_1_1_008' as serial_number
        union all select '退货_C端_苏州仓_苏宁渠道'        as combination_primary,'2_1_1_009' as serial_number
        union all select '退货_C端_苏州仓_凯叔优选渠道'    as combination_primary,'2_1_1_010' as serial_number
        union all select '退货_C端_苏州仓_客服权益'        as combination_primary,'2_1_1_011' as serial_number
        union all select '退货_C端_苏州仓_抖音旗舰店渠道'  as combination_primary,'2_1_1_012' as serial_number
        union all select '退货_C端_苏州仓_富集商城渠道'    as combination_primary,'2_1_1_013' as serial_number
        union all select '退货_C端_苏州仓_腾讯惠聚渠道'    as combination_primary,'2_1_1_014' as serial_number
        union all select '退货_C端_苏州仓_微信小商店渠道'  as combination_primary,'2_1_1_015' as serial_number
        union all select '退货_C端_苏州仓_苏宁天猫渠道'    as combination_primary,'2_1_1_016' as serial_number
        union all select '退货_C端_苏州仓_拼多多分销'      as combination_primary,'2_1_1_024' as serial_number
        union all select '退货_C端_苏州仓_唯品会分销'      as combination_primary,'2_1_1_025' as serial_number

        union all select '退货_C端_广州仓_官方商城渠道'    as combination_primary,'2_1_2_001' as serial_number
        union all select '退货_C端_广州仓_自营天猫渠道'    as combination_primary,'2_1_2_002' as serial_number
        union all select '退货_C端_广州仓_网络分销渠道'    as combination_primary,'2_1_2_005' as serial_number
        union all select '退货_C端_广州仓_京东渠道'        as combination_primary,'2_1_2_006' as serial_number
        union all select '退货_C端_广州仓_自营拼多多渠道'  as combination_primary,'2_1_2_007' as serial_number
        union all select '退货_C端_广州仓_苏宁渠道'        as combination_primary,'2_1_2_009' as serial_number
        union all select '退货_C端_广州仓_抖音旗舰店渠道'  as combination_primary,'2_1_2_012' as serial_number
        union all select '退货_C端_广州仓_拼多多分销'      as combination_primary,'2_1_2_024' as serial_number

        union all select '退货_C端_苏州电商A仓_成人粉抖音渠道'      as combination_primary,'2_1_3_017' as serial_number
        union all select '退货_C端_苏州电商A仓_成人粉京东渠道'      as combination_primary,'2_1_3_019' as serial_number
        union all select '退货_C端_苏州电商A仓_成人粉天猫渠道'      as combination_primary,'2_1_3_022' as serial_number
        union all select '退货_C端_苏州电商A仓_成人粉京东POP'       as combination_primary,'2_1_3_026' as serial_number
        union all select '退货_C端_苏州电商A仓_成人粉唯品MP'        as combination_primary,'2_1_3_027' as serial_number
        union all select '退货_C端_苏州电商A仓_成人粉淘特渠道'      as combination_primary,'2_1_3_028' as serial_number
        union all select '退货_C端_苏州电商A仓_成人粉-天猫爱本官旗' as combination_primary,'2_1_3_031' as serial_number
        union all select '退货_C端_苏州电商A仓_成人粉-微信小程序'   as combination_primary,'2_1_3_032' as serial_number

        union all select '退货_C端_广州电商A仓_成人粉抖音渠道'      as combination_primary,'2_1_4_017' as serial_number
        union all select '退货_C端_广州电商A仓_成人粉天猫渠道'      as combination_primary,'2_1_4_022' as serial_number
        union all select '退货_C端_广州电商A仓_成人粉京东POP'       as combination_primary,'2_1_4_026' as serial_number
        union all select '退货_C端_广州电商A仓_成人粉唯品MP'        as combination_primary,'2_1_4_027' as serial_number

        union all select '退货_B端_苏州仓_天猫超市渠道'      as combination_primary,'2_2_1_003' as serial_number
        union all select '退货_B端_苏州仓_京东渠道'          as combination_primary,'2_2_1_006' as serial_number
        union all select '退货_B端_苏州仓_考拉渠道'          as combination_primary,'2_2_1_008' as serial_number
        union all select '退货_B端_苏州仓_苏宁渠道'          as combination_primary,'2_2_1_009' as serial_number
        union all select '退货_B端_苏州仓_成人粉-饿了么渠道' as combination_primary,'2_2_1_030' as serial_number
        union all select '退货_B端_苏州仓_京东健康渠道'      as combination_primary,'2_2_1_111' as serial_number

        union all select '退货_B端_广州仓_天猫超市渠道' as combination_primary,'2_2_2_003' as serial_number
        union all select '退货_B端_广州仓_京东渠道'     as combination_primary,'2_2_2_006' as serial_number
        union all select '退货_B端_广州仓_考拉渠道'     as combination_primary,'2_2_2_008' as serial_number
        union all select '退货_B端_广州仓_苏宁渠道'     as combination_primary,'2_2_2_009' as serial_number
        union all select '退货_B端_广州仓_京东健康渠道' as combination_primary,'2_2_2_111' as serial_number

        union all select '退货_B端_苏州电商A仓_成人粉猫超渠道'    as combination_primary,'2_2_3_018' as serial_number
        union all select '退货_B端_苏州电商A仓_成人粉京东渠道'    as combination_primary,'2_2_3_019' as serial_number
        union all select '退货_B端_苏州电商A仓_成人粉分销'        as combination_primary,'2_2_3_023' as serial_number
        union all select '退货_B端_苏州电商A仓_成人粉-饿了么渠道' as combination_primary,'2_2_3_030' as serial_number

        union all select '退货_B端_广州电商A仓_成人粉猫超渠道'    as combination_primary,'2_2_4_018' as serial_number
        union all select '退货_B端_广州电商A仓_成人粉天猫渠道'    as combination_primary,'2_2_4_022' as serial_number
    ) b1
        left join
    (
        select
            sales_type
             ,warehouse_name
             ,channel_name
             ,order_type
             ,send_quantity
        from
            (
                select
                    case
                        when order_code like 'SO%' or order_code like 'XC%'
                            then 'B端'
                        else 'C端'
                        end as sales_type
                     ,if(channel_name in ('经销渠道','代销渠道'),'网络分销渠道',channel_name) as channel_name
                     ,case
                          when warehouse_name like '苏州电商A%' then '苏州电商A仓'
                          when warehouse_name like '广州电商A%' then '广州电商A仓'
                          when warehouse_name like '上海%' or warehouse_name like '苏州%' then '苏州仓'
                          when warehouse_name like '广州%' then '广州仓'
                    end as warehouse_name
                     ,order_type
                     ,sum(if(order_type='退货',-send_quantity,send_quantity)) as send_quantity
                     ,sum(if(order_type='退货',-actual_paid_fee,actual_paid_fee)) as actual_paid_fee
                     ,sum(if(order_type='退货',-total_weight,total_weight)) as total_weight
                     ,sum(if(order_type='退货',-actual_paid_fee -post_fee ,actual_paid_fee +post_fee)) as income_amount
                from ads_bi_ec_delivery_return_detail
                     -- where DATE_FORMAT(send_time,'%Y-%m-%d') >= '${start_date}'
                     -- and DATE_FORMAT(send_time,'%Y-%m-%d')<= '${end_date}'
                group by
                    case
                        when order_code like 'SO%' or order_code like 'XC%'
                            then 'B端'
                        else 'C端'
                        end
                       ,if(channel_name in ('经销渠道','代销渠道'),'网络分销渠道',channel_name)
                       ,case
                            when warehouse_name like '苏州电商A%' then '苏州电商A仓'
                            when warehouse_name like '广州电商A%' then '广州电商A仓'
                            when warehouse_name like '上海%' or warehouse_name like '苏州%' then '苏州仓'
                            when warehouse_name like '广州%' then '广州仓'
                    end
                       ,order_type
            ) a
    ) b2
    on b1.combination_primary = concat(b2.order_type,'_',b2.sales_type,'_',warehouse_name,'_',channel_name)
order by serial_number;