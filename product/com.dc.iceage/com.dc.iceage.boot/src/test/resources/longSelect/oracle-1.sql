SELECT
    obj.owner as "owner",
    obj.object_name as "object_name",
    obj.object_type as "object_type" ,
    (case when obj.status = 'INVALID' then 1 else 0 end) as "status",
    (case when obj.object_type = 'TABLE' then (
        select comments from all_tab_comments b where b.table_name=obj.object_name and b.owner=obj.owner and rownum=1
    ) else '' end) as "comment",
    (case
         when obj.object_type IN ('FUNCTION','PROCEDURE','PACKAGE BODY','PACKAGE') and (select PLSQL_DEBUG from ALL_PLSQL_OBJECT_SETTINGS obj_setting where obj.OWNER = obj_setting.OWNER AND obj.OBJECT_TYPE = obj_setting.TYPE AND obj.OBJECT_NAME = obj_setting.NAME and rownum=1)='TRUE' THEN '1' else '0' end) as "debug_info"
from (
         select owner, object_name, object_type, status from (
                                                                 select rownum num,whd_t.* from (
                                                                                                    select obj.owner, obj.object_name, obj.object_type, obj.status
                                                                                                    from all_objects obj
                                                                                                    where 1=1 and
                                                                                                            obj.object_type <>'JOB'  and obj.owner not in ('PUBLIC')  and obj.owner in ('B3')  and obj.object_type in ('TABLE')
                                                                                                    ORDER BY obj.object_name,obj.owner ASC
                                                                                                ) whd_t where rownum <=10000) whd_t where whd_t.num>9900
     ) obj;