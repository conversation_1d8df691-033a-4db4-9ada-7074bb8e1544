SELECT
  TO_CHAR (a.USER<PERSON>) as <PERSON>ER<PERSON>,
  TO_CHAR (a.URL) as URL,
  TO_CHAR (a.RESTYPE) as RESTYP<PERSON>,
  TO_CHAR (a.ICONC<PERSON>) as <PERSON>CONC<PERSON>,
  TO_CHAR (a.SEQ) as SEQ,
  TO_CHAR (a.<PERSON>VOKE<PERSON><PERSON>) as IN<PERSON><PERSON><PERSON><PERSON>,
  TO_CHAR (a.IS<PERSON><PERSON><PERSON>ME<PERSON>) as ISRIGHTMENU
FROM
  (
    SELECT
      A.USERID AS USERID,
      C.URL AS URL,
      C.RESTYPE AS RESTYPE,
      C.ICONCIS AS ICONCIS,
      C.SEQ AS SEQ,
      C.INVOKEFUN AS INVOKEFUN,
      C.ISRIGHTMENU AS ISRIGHTMENU
    FROM
      CP_SYS_USER_ROLE A
      LEFT JOIN CP_SYS_ROLE_RESOURCE B ON ((A.ROLEID = B.ROLEID))
      LEFT JOIN CP_SYS_RESOURCE C ON ((B.RESID = C.ID))
      LEFT JOIN CP_SYS_USER D ON ((A.USERID = D.ID))
    WHERE
      C.ISDELETED = 0
      AND D.ISDELETED = 0
  ) a
union all
SELECT
  TO_CHAR (c.ENTITYID) as USER<PERSON>,
  TO_CHAR (c.MODELID) as URL,
  TO_CHAR (c.USERNAME) as RESTYPE,
  TO_CHAR (c.PASSWORD) as ICONCIS,
  TO_CHAR (c.ISMONITOR) as SEQ,
  TO_CHAR (c.NODEID) as INVOKEFUN,
  TO_CHAR (c.PORT) as ISRIGHTMENU
from
  (
    SELECT
      A.ENTITYID AS ENTITYID,
      MODEL.MODELID AS MODELID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITORIP' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS IP,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.USERNAME' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS USERNAME,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.PASSWORD' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PASSWORD,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITORSTATUS' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS ISMONITOR,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.COLLECT.NODE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS NODEID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITOR.PORT' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PORT
    FROM
      CP_CI_ENTITY A
      JOIN CP_CI_MODEL MODEL ON A.MODELID = MODEL.MODELID
      AND MODEL.MONITORTYPE = 1073741824
      AND MODEL.BUSSTYPE = 0
      JOIN CP_CI_ENTITY_PROP PROP ON ((A.ENTITYID = PROP.ENTITYID))
      JOIN CP_CI_PROPS PROPS ON ((PROP.PROPERTYID = PROPS.PROPERTYID))
    WHERE
      A.ISDELETED = 0
      AND PROP.ISDELETED = 0
      AND PROPS.ISDELETED = 0
    GROUP BY
      A.ENTITYID,
      MODEL.MODELID
  ) c
union all
SELECT
  TO_CHAR (d.ENTITYID) as USERID,
  TO_CHAR (d.PORT) as URL,
  TO_CHAR (d.MODELID) as RESTYPE,
  TO_CHAR (d.USERS) as ICONCIS,
  TO_CHAR (d.NODEID) as SEQ,
  TO_CHAR (d.PASSWORD) as INVOKEFUN,
  TO_CHAR (d.MONITORSTATUS) as ISRIGHTMENU
FROM
  (
    SELECT
      T.ENTITYID AS ENTITYID,
      T.MODELID AS MODELID,
      MAX(T.IP) AS IP,
      MAX(T.PORT) AS PORT,
      MAX(T.USERS) AS USERS,
      MAX(T.PASSWORD) AS PASSWORD,
      MAX(T.MONITORSTATUS) AS MONITORSTATUS,
      MAX(T.NODEID) AS NODEID
    FROM
      (
        SELECT
          EP.ENTITYID AS ENTITYID,
          E.MODELID AS MODELID,
          (
            CASE
              WHEN P.PROPERTYKEY = 'ENT.MONITORIP' THEN EP.PROPERTYVALUE
              ELSE NULL
            END
          ) AS IP,
          (
            CASE
              WHEN P.PROPERTYKEY = 'ENT.MONITOR.PORT' THEN EP.PROPERTYVALUE
              ELSE NULL
            END
          ) AS PORT,
          (
            CASE
              WHEN P.PROPERTYKEY = 'ENT.USERNAME' THEN EP.PROPERTYVALUE
              ELSE NULL
            END
          ) AS USERS,
          (
            CASE
              WHEN P.PROPERTYKEY = 'ENT.PASSWORD' THEN EP.PROPERTYVALUE
              ELSE NULL
            END
          ) AS PASSWORD,
          (
            CASE
              WHEN P.PROPERTYKEY = 'ENT.MONITORSTATUS' THEN EP.PROPERTYVALUE
              ELSE NULL
            END
          ) AS MONITORSTATUS,
          (
            CASE
              WHEN P.PROPERTYKEY = 'ENT.COLLECT.NODE' THEN EP.PROPERTYVALUE
              ELSE NULL
            END
          ) AS NODEID
        FROM
          (
            (
              (
                HTSC.CP_CI_ENTITY_PROP EP
                LEFT JOIN HTSC.CP_CI_ENTITY E ON EP.ENTITYID = E.ENTITYID
              )
              LEFT JOIN HTSC.CP_CI_PROPS P ON EP.PROPERTYID = P.PROPERTYID
            )
            LEFT JOIN HTSC.CP_CI_MODEL M ON E.MODELID = M.MODELID
          )
        WHERE
          E.MODELID = 240
          AND EP.ISDELETED = 0
          AND E.ISDELETED = 0
          AND P.ISDELETED = 0
      ) T
    GROUP BY
      T.ENTITYID,
      T.MODELID
  ) d
union all
SELECT
  TO_CHAR (b.USERID) as USERID,
  TO_CHAR (b.ORGID) as ORGID,
  '1' as RESTYPE,
  '1' as ICONCIS,
  '1' as SEQ,
  '1' as INVOKEFUN,
  '1' as ISRIGHTMENU
FROM
  (
    SELECT
      USR.ID AS USERID,
      ORG.ID AS ORGID
    FROM
      CP_SYS_USER USR
      JOIN CP_SYS_USER_ROLE USR_ROLE ON USR.ID = USR_ROLE.USERID
      LEFT JOIN CP_SYS_ROLE ROLE ON ROLE.ID = USR_ROLE.ROLEID
      LEFT JOIN CP_SYS_ORG ORG ON ROLE.ORGID = ORG.ID
    GROUP BY
      USR.ID,
      ORG.ID
  ) b
union all
SELECT
  TO_CHAR (e.PROVIDERNAME) as USERID,
  TO_CHAR (e.URL) as URL,
  TO_CHAR (e.PASSWORD) as RESTYPE,
  TO_CHAR (e.USERNAME) as ICONCIS,
  TO_CHAR (e.NAMESPACE) as SEQ,
  TO_CHAR (e.NAMESPACE) as INVOKEFUN,
  TO_CHAR (e.NAMESPACE) as ISRIGHTMENU
FROM
  (
    SELECT
      DISTINCT MAX(
        (
          CASE
            WHEN PROPS.PROPERTYKEY = 'ENT.PROVIDER.NAME' THEN PROP.PROPERTYVALUE
            ELSE NULL
          END
        )
      ) AS PROVIDERNAME,
      MAX(
        (
          CASE
            WHEN PROPS.PROPERTYKEY = 'ENT.URL' THEN PROP.PROPERTYVALUE
            ELSE NULL
          END
        )
      ) AS URL,
      MAX(
        (
          CASE
            WHEN PROPS.PROPERTYKEY = 'ENT.USERNAME' THEN PROP.PROPERTYVALUE
            ELSE NULL
          END
        )
      ) AS USERNAME,
      MAX(
        (
          CASE
            WHEN PROPS.PROPERTYKEY = 'ENT.PASSWORD' THEN PROP.PROPERTYVALUE
            ELSE NULL
          END
        )
      ) AS PASSWORD,
      MAX(
        (
          CASE
            WHEN PROPS.PROPERTYKEY = 'ENT.NAMESPACE' THEN PROP.PROPERTYVALUE
            ELSE NULL
          END
        )
      ) AS NAMESPACE
    FROM
      (
        (
          (
            CP_CI_ENTITY A
            JOIN CP_CI_MODEL M ON (
              (
                (A.MODELID = M.MODELID)
                AND (M.MONITORTYPE = 8192)
                AND (M.BUSSTYPE IN (1, 2, 3, 4))
              )
            )
          )
          JOIN CP_CI_ENTITY_PROP PROP ON ((A.ENTITYID = PROP.ENTITYID))
        )
        JOIN CP_CI_PROPS PROPS ON ((PROP.PROPERTYID = PROPS.PROPERTYID))
      )
    WHERE
      (A.ISVERIFY = 1)
    GROUP BY
      A.ENTITYID
  ) e
union all
SELECT
  TO_CHAR (f.entityid) as USERID,
  TO_CHAR (f.isverify) as URL,
  TO_CHAR (f.modelid) as RESTYPE,
  TO_CHAR (f.password) as ICONCIS,
  TO_CHAR (f.port) as SEQ,
  TO_CHAR (f.isUse) as INVOKEFUN,
  TO_CHAR (f.MonitorStatus) as ISRIGHTMENU
FROM
  (
    SELECT
      a.EntityID AS entityid,
      a.IsVerify AS isverify,
      a.CreateTime AS CreateTime,
      model.ModelID AS modelid,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.monitorip' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS ip,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.monitor.port' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS port,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.username' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS USERS,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.password' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS password,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.monitorstatus' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS MonitorStatus,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.collect.node' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS NodeID,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.tomcat.is.auth' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS isUse
    FROM
      (
        (
          (
            cp_ci_entity a
            JOIN cp_ci_model model ON (
              (
                (a.ModelID = model.ModelID)
                AND (model.MonitorType = 134217728)
              )
            )
          )
          JOIN cp_ci_entity_prop prop ON ((a.EntityID = prop.EntityID))
        )
        JOIN cp_ci_props props ON ((prop.PropertyID = props.PropertyID))
      )
    WHERE
      (
        (a.IsDeleted = 0)
        AND (prop.IsDeleted = 0)
        AND (props.IsDeleted = 0)
      )
    GROUP BY
      a.EntityID,
      a.IsVerify,
      a.CreateTime,
      model.ModelID
  ) f
union all
SELECT
  TO_CHAR (g.PoolID) as USERID,
  TO_CHAR (g.PoolName) as URL,
  TO_CHAR (g.PoolName) as ISRIGHTMENU,
  TO_CHAR (g.PoolName) as RESTYPE,
  TO_CHAR (g.PoolName) as ICONCIS,
  TO_CHAR (g.PoolName) as SEQ,
  TO_CHAR (g.PoolName) as INVOKEFUN
FROM
  (
    SELECT
      e.EntityID AS PoolID,
      max(
        CASE
          WHEN props.PropertyKey = 'ent.name' THEN prop.PropertyValue
          ELSE NULL
        END
      ) AS PoolName
    FROM
      htsc.cp_ci_entity e
      JOIN htsc.cp_ci_model m ON e.ModelID = m.ModelID
      AND m.MonitorType = 8192
      AND m.BussType = 102
      JOIN htsc.cp_ci_entity_nexus n ON e.EntityID = n.EntityID
      AND n.NexusType = 3
      JOIN htsc.cp_ci_entity_prop prop ON e.EntityID = prop.EntityID
      JOIN htsc.cp_ci_props props ON prop.PropertyID = props.PropertyID
    GROUP BY
      e.EntityID
  ) g
union all
SELECT
  TO_CHAR (j.DISKID) as USERID,
  TO_CHAR (j.DISKNAME) as URL,
  TO_CHAR (j.DISKPURPOSE) as ISRIGHTMENU,
  TO_CHAR (j.CONNECTTYPE) as RESTYPE,
  '1' as ICONCIS,
  '1' as SEQ,
  '1' as INVOKEFUN
FROM
  (
    SELECT
      E.ENTITYID AS DISKID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.NAME' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS DISKNAME,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.INTERCONNECT.TYPE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS CONNECTTYPE,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.PURPOSE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS DISKPURPOSE
    FROM
      HTSC.CP_CI_ENTITY E
      JOIN HTSC.CP_CI_MODEL M ON E.MODELID = M.MODELID
      AND M.MONITORTYPE = 8192
      AND M.BUSSTYPE = 101
      JOIN HTSC.CP_CI_ENTITY_NEXUS N ON E.ENTITYID = N.ENTITYID
      JOIN HTSC.CP_CI_ENTITY_PROP PROP ON E.ENTITYID = PROP.ENTITYID
      JOIN HTSC.CP_CI_PROPS PROPS ON PROP.PROPERTYID = PROPS.PROPERTYID
    GROUP BY
      E.ENTITYID
  ) j
union all
SELECT
  TO_CHAR (h.LUNID) as USERID,
  TO_CHAR (h.LUNSPU) as URL,
  TO_CHAR (h.LUNDEVICE) as ISRIGHTMENU,
  TO_CHAR (h.LUNTYPE) as RESTYPE,
  TO_CHAR (h.LUNNAME) as ICONCIS,
  TO_CHAR (h.LUNPROTOCOL) as SEQ,
  TO_CHAR (h.RAIDTYPE) as INVOKEFUN
FROM
  (
    SELECT
      E.ENTITYID AS LUNID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.LUN.DEVICE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS LUNDEVICE,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.NAME' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS LUNNAME,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.LUN.SPU' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS LUNSPU,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.LUN.TYPE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS LUNTYPE,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.LUN.PROTOCOL' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS LUNPROTOCOL,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.LUN.RAIDTYPE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS RAIDTYPE
    FROM
      HTSC.CP_CI_ENTITY E
      JOIN HTSC.CP_CI_MODEL M ON E.MODELID = M.MODELID
      AND M.MONITORTYPE = 8192
      AND M.BUSSTYPE = 103
      JOIN HTSC.CP_CI_ENTITY_NEXUS N ON E.ENTITYID = N.ENTITYID
      JOIN HTSC.CP_CI_ENTITY_PROP PROP ON E.ENTITYID = PROP.ENTITYID
      JOIN HTSC.CP_CI_PROPS PROPS ON PROP.PROPERTYID = PROPS.PROPERTYID
    GROUP BY
      E.ENTITYID
  ) h
union all
SELECT
  TO_CHAR (T.ENTITYID) AS USERID,
  TO_CHAR (T.MODELID) AS URL,
  TO_CHAR (MAX(T.SNMPDEVICETYPE)) AS ISRIGHTMENU,
  TO_CHAR (MAX(T.AUTHTYPE)) AS RESTYPE,
  TO_CHAR (MAX(T.AUTHPASSWORD)) AS ICONCIS,
  TO_CHAR (MAX(T.PRIVTYPE)) AS SEQ,
  TO_CHAR (MAX(T.PRIVPASSWORD)) AS INVOKEFUN
FROM
  (
    SELECT
      EP.ENTITYID AS ENTITYID,
      E.MODELID AS MODELID,
      M.MONITORTYPE AS SNMPDEVICETYPE,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.MONITORSTATUS' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS MONITORSTATUS,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.AUTHTYPE' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SNMPVERSION,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.SNMPCOMMUNITY' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SNMPCOMMUNITY,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.SNMPUSERNAME' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SNMPPWD,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.SNMPPWD' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SNMPUSERNAME,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.MONITORIP' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS WKIP,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.HOSTNAME' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS HOSTNAME,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.SAFETY.LEVEL' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SAFETYLEVEL,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.USER.NAME' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS USERNAME,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.ALGORITHM.TYPE' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS AUTHTYPE,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.PWD' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS AUTHPASSWORD,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.ENCRYPT.TYPE' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PRIVTYPE,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.ENCRYPT.PWD' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PRIVPASSWORD,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.SNMP.PORT' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SNMPPORT,
      (
        CASE
          WHEN P.PROPERTYKEY = 'ENT.COLLECT.NODE' THEN EP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS NODEID
    FROM
      (
        (
          (
            HTSC.CP_CI_ENTITY_PROP EP
            LEFT JOIN HTSC.CP_CI_ENTITY E ON ((EP.ENTITYID = E.ENTITYID))
          )
          LEFT JOIN HTSC.CP_CI_PROPS P ON ((EP.PROPERTYID = P.PROPERTYID))
        )
        LEFT JOIN HTSC.CP_CI_MODEL M ON ((E.MODELID = M.MODELID))
      )
    WHERE
      (
        (
          (M.PID = 10)
          OR (
            (M.MONITORTYPE = 4398046511104)
            AND (M.BUSSTYPE IN (1, 6))
          )
        )
        AND (EP.ISDELETED = 0)
        AND (E.ISDELETED = 0)
        AND (P.ISDELETED = 0)
      )
  ) T
GROUP BY
  T.ENTITYID,
  T.MODELID
union all
SELECT
  TO_CHAR (y.ENTITYID) AS USERID,
  TO_CHAR (y.ORGID) AS URL,
  TO_CHAR (y.MODELID) AS ISRIGHTMENU,
  TO_CHAR (y.ISVERIFY) AS RESTYPE,
  TO_CHAR (y.POSITIONID) AS ICONCIS,
  TO_CHAR (y.LASTUPDATEUSER) AS SEQ,
  TO_CHAR (y.POSITIONID) AS INVOKEFUN
FROM
  HTSC.CP_CI_ENTITY y
union all
SELECT
  TO_CHAR (X.ENTITYID) AS USERID,
  TO_CHAR (X.NAME) AS URL,
  TO_CHAR (X.MONITORSTATUS) AS RESTYPE,
  TO_CHAR (X.SERVER) AS ICONCIS,
  TO_CHAR (X.LOGPOSITION) AS SEQ,
  TO_CHAR (X.ISDELETE) AS INVOKEFUN,
  '1' as ISRIGHTMENU
FROM
  (
    SELECT
      A.ENTITYID AS ENTITYID,
      A.IDENTITYCODE AS IDENTITYCODE,
      MAX(
        CASE
          WHEN PS.PROPERTYKEY = 'ENT.MONITORSTATUS' THEN P.PROPERTYVALUE
          ELSE NULL
        END
      ) AS MONITORSTATUS,
      MAX(
        CASE
          WHEN PS.PROPERTYKEY = 'ENT.NAME' THEN P.PROPERTYVALUE
          ELSE NULL
        END
      ) AS NAME,
      MAX(
        CASE
          WHEN PS.PROPERTYKEY = 'ENT.KETTLE.EXECUTING.SEVER' THEN P.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SERVER,
      MAX(
        CASE
          WHEN PS.PROPERTYKEY = 'ENT.KETTLE.LOG.POSITION' THEN P.PROPERTYVALUE
          ELSE NULL
        END
      ) AS LOGPOSITION,
      A.ISDELETED AS ISDELETE
    FROM
      HTSC.CP_CI_ENTITY A
      JOIN HTSC.CP_CI_ENTITY_PROP P ON A.ENTITYID = P.ENTITYID
      JOIN HTSC.CP_CI_MODEL M ON A.MODELID = M.MODELID
      AND M.MONITORTYPE = 536870912
      JOIN HTSC.CP_CI_PROPS PS ON PS.PROPERTYID = P.PROPERTYID
      AND PS.PROPERTYKEY IN (
        'ENT.NAME',
        'ENT.MONITORSTATUS',
        'ENT.KETTLE.LOG.POSITION',
        'ENT.KETTLE.EXECUTING.SEVER'
      )
    GROUP BY
      A.ENTITYID,
      A.IDENTITYCODE,
      A.ISDELETED
    ORDER BY
      A.ENTITYID DESC
  ) X
union all
SELECT
  TO_CHAR (z.ENTITYID) AS USERID,
  TO_CHAR (z.IP) AS URL,
  TO_CHAR (z.MODELID) AS ISRIGHTMENU,
  TO_CHAR (z.ISON) AS RESTYPE,
  TO_CHAR (z.PORT) AS ICONCIS,
  TO_CHAR (z.MONITORSTATUS) AS SEQ,
  TO_CHAR (z.SERVERCHANNEL) AS INVOKEFUN
FROM
  (
    SELECT
      A.ENTITYID AS ENTITYID,
      A.ISVERIFY AS ISVERIFY,
      A.CREATETIME AS CREATETIME,
      MODEL.MODELID AS MODELID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITORIP' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS IP,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITOR.PORT' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PORT,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.USERNAME' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS USERS,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.PASSWORD' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PASSWORD,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITORSTATUS' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS MONITORSTATUS,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MQ.QUEUE.MANAGER' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS QUEUEMANAGE,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MQ.SERVERCHANNEL' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS SERVERCHANNEL,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MQ.AUTHENTICATION' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS ISON,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MQ.CCSID' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS CCSID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.COLLECT.NODE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS NODEID
    FROM
      CP_CI_ENTITY A
      JOIN CP_CI_MODEL MODEL ON A.MODELID = MODEL.MODELID
      AND MODEL.MONITORTYPE = 268435456
      JOIN CP_CI_ENTITY_PROP PROP ON A.ENTITYID = PROP.ENTITYID
      JOIN CP_CI_PROPS PROPS ON PROP.PROPERTYID = PROPS.PROPERTYID
    WHERE
      A.ISDELETED = 0
      AND PROP.ISDELETED = 0
      AND PROPS.ISDELETED = 0
    GROUP BY
      A.ENTITYID,
      A.ISVERIFY,
      A.CREATETIME,
      MODEL.MODELID
  ) z
union all
SELECT
  TO_CHAR (m.ENTITYID) AS USERID,
  TO_CHAR (m.ISVERIFY) AS URL,
  TO_CHAR (m.PORT) AS ISRIGHTMENU,
  TO_CHAR (m.BUSSTYPE) AS RESTYPE,
  TO_CHAR (m.ISMONITOR) AS ICONCIS,
  TO_CHAR (m.NODEID) AS SEQ,
  TO_CHAR (m.CARDNUMBER) AS INVOKEFUN
FROM
  (
    SELECT
      E.ENTITYID AS ENTITYID,
      E.ISVERIFY AS ISVERIFY,
      MAX(M.BUSSTYPE) AS BUSSTYPE,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITORIP' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS IP,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.USERNAME' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS USERNAME,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.PASSWORD' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PASSWORD,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITOR.PORT' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PORT,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITORSTATUS' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS ISMONITOR,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.COLLECT.NODE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS NODEID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.NW.NUM' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS CARDNUMBER,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.HOST.CONNECT.TYPE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS HOSTCONNECTTYPE
    FROM
      CP_CI_ENTITY E
      JOIN CP_CI_MODEL M ON E.MODELID = M.MODELID
      AND M.MONITORTYPE = 1
      AND E.ISDELETED = 0
      JOIN CP_CI_ENTITY_PROP PROP ON E.ENTITYID = PROP.ENTITYID
      AND E.ISDELETED = 0
      JOIN CP_CI_PROPS PROPS ON PROP.PROPERTYID = PROPS.PROPERTYID
    GROUP BY
      E.ENTITYID,
      ISVERIFY
  ) m
union all
SELECT
  TO_CHAR (s.DEVICEID) AS USERID,
  TO_CHAR (s.PROPERTYID) AS URL,
  TO_CHAR (s.AUDITSTATUS) AS ISRIGHTMENU,
  TO_CHAR (s.MONITORSTATUS) AS RESTYPE,
  TO_CHAR (s.HOSTIP) AS ICONCIS,
  TO_CHAR (s.DEVICENAME) AS SEQ,
  TO_CHAR (s.HOSTTIMEOFFSET) AS INVOKEFUN
FROM
  (
    SELECT
      EP.ENTITYID AS DEVICEID,
      EP.PROPERTYID AS PROPERTYID,
      E.AUDITSTATUS AS AUDITSTATUS,
      E.MONITORSTATUS AS MONITORSTATUS,
      CASE
        WHEN P.PROPERTYKEY = 'ENT.WK.IP' THEN EP.PROPERTYVALUE
        ELSE NULL
      END AS HOSTIP,
      CASE
        WHEN P.PROPERTYKEY = 'ENT.NAME' THEN EP.PROPERTYVALUE
        ELSE NULL
      END AS DEVICENAME,
      CASE
        WHEN P.PROPERTYKEY = 'ENT.TIME.OFFSET' THEN EP.PROPERTYVALUE
        ELSE NULL
      END AS HOSTTIMEOFFSET
    FROM
      CP_CI_ENTITY_PROP EP
      LEFT JOIN CP_CI_ENTITY E ON EP.ENTITYID = E.ENTITYID
      LEFT JOIN CP_CI_PROPS P ON EP.PROPERTYID = P.PROPERTYID
    WHERE
      (
        E.MODELID IN (
          SELECT
            CP_CI_MODEL.MODELID
          FROM
            CP_CI_MODEL
          WHERE
            CP_CI_MODEL.MONITORTYPE = '1'
            AND CP_CI_MODEL.BUSSTYPE = '0'
        )
        AND (
          (P.PROPERTYKEY = 'ENT.WK.IP')
          OR (P.PROPERTYKEY = 'ENT.NAME')
          OR (P.PROPERTYKEY = 'ENT.TIME.OFFSET')
        )
        AND (EP.ISDELETED = 0)
        AND (E.ISDELETED = 0)
        AND (P.ISDELETED = 0)
      )
  ) s
union all
SELECT
  TO_CHAR (oo.ENTITYID) AS USERID,
  TO_CHAR (oo.USERs) AS URL,
  TO_CHAR (oo.NODEID) AS ISRIGHTMENU,
  TO_CHAR (oo.PASSWORD) AS RESTYPE,
  TO_CHAR (oo.PORT) AS ICONCIS,
  TO_CHAR (oo.IP) AS SEQ,
  TO_CHAR (oo.MONITORSTATUS) AS INVOKEFUN
FROM
  (
    SELECT
      E.ENTITYID AS ENTITYID,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITORSTATUS' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS MONITORSTATUS,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.WKIP' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS IP,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.MONITOR.PORT' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS PORT,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.USERNAME' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS USERs,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.PASSWORD' THEN PROP.PROPERTYVALUE
          ELSE NULL
        end
      ) AS PASSWORD,
      MAX(
        CASE
          WHEN PROPS.PROPERTYKEY = 'ENT.COLLECT.NODE' THEN PROP.PROPERTYVALUE
          ELSE NULL
        END
      ) AS NODEID
    from
      CP_CI_ENTITY E
      JOIN CP_CI_MODEL M ON E.MODELID = M.MODELID
      AND M.MONITORTYPE = 4194304
      JOIN CP_CI_ENTITY_PROP PROP ON E.ENTITYID = PROP.ENTITYID
      AND E.ISDELETED = 0
      JOIN CP_CI_PROPS PROPS ON PROP.PROPERTYID = PROPS.PROPERTYID
    GROUP BY
      E.ENTITYID
  ) oo
union all
SELECT
  TO_CHAR (ww.ENTITYID) AS USERID,
  TO_CHAR (ww.REQURL) AS URL,
  TO_CHAR (ww.ISMONITOR) AS ISRIGHTMENU,
  TO_CHAR (ww.REQMETHOD) AS RESTYPE,
  TO_CHAR (ww.IFNAME) AS ICONCIS,
  TO_CHAR (ww.REQHOST) AS SEQ,
  TO_CHAR (ww.REQINTERVALUNIT) AS INVOKEFUN
FROM
  (
    select
      e.ENTITYID AS ENTITYID,
      max(
        case
          when props.PROPERTYKEY = 'ent.name' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS IFNAME,
      max(
        case
          when props.PROPERTYKEY = 'ent.monitorstatus' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS ISMONITOR,
      max(
        case
          when props.PROPERTYKEY = 'ent.dt.url' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS REQMETHOD,
      max(
        case
          when props.PROPERTYKEY = 'ent.dt.url' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS REQURL,
      max(
        case
          when props.PROPERTYKEY = 'ent.reqInterval' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS REQINTERVAL,
      max(
        case
          when props.PROPERTYKEY = 'ent.reqInterval' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS REQINTERVALUNIT,
      max(
        case
          when props.PROPERTYKEY = 'ent.authtimeout' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS RESTIMEOUT,
      max(3) AS RESTIMEOUTUNIT,
      max(
        case
          when props.PROPERTYKEY = 'ent.monitorip' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS REQHOST,
      max(
        case
          when props.PROPERTYKEY = 'ent.monitor.port' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS REQPORT,
      max(
        case
          when props.PROPERTYKEY = 'ent.check.status' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS ISTESTOK,
      max(
        case
          when props.PROPERTYKEY = 'ent.req.params' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS REQPARAMETERS,
      max(
        case
          when props.PROPERTYKEY = 'ent.ana.result' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS EXINFO,
      max(
        case
          when props.PROPERTYKEY = 'ent.collect.node' then prop.PROPERTYVALUE
          else NULL
        end
      ) AS NODEID
    from
      (
        (
          (
            HTSC.cp_ci_entity e
            join HTSC.cp_ci_model m on(
              (
                (e.MODELID = m.MODELID)
                and (m.MONITORTYPE = 512)
                and (m.BUSSTYPE in (1, 2, 3))
              )
            )
          )
          join HTSC.cp_ci_entity_prop prop on(
            (
              (e.ENTITYID = prop.ENTITYID)
              and (e.ISDELETED = 0)
            )
          )
        )
        join HTSC.cp_ci_props props on((prop.PROPERTYID = prop.PROPERTYID))
      )
    group by
      e.ENTITYID
  ) ww