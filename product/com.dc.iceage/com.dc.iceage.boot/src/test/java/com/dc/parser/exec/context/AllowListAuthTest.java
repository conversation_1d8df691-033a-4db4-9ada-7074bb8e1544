package com.dc.parser.exec.context;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.exec.check.SQLFlexCheckEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngineFactory;
import com.dc.parser.model.check.flex.auth.AllowListAuth;
import com.dc.parser.model.check.flex.auth.CheckAuthParam;
import com.dc.parser.model.check.flex.auth.CheckAuthResult;
import com.dc.parser.model.check.flex.auth.SQLAuth;
import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.statement.SQLStatement;
import org.junit.jupiter.api.Test;

class AllowListAuthTest {

    public SQLStatement test(DatabaseType.Constant constant, String sql) {
        DatabaseType databaseType = TypedSPILoader.getService(DatabaseType.class, constant);
        SQLStatementParserEngine sqlStatementParserEngine = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(64, 1024L));
        return sqlStatementParserEngine.parse(sql, true);
    }

    @Test
    void setSchemas() {
//        DatabaseType.Constant constant = DatabaseType.Constant.PG_SQL;
        DatabaseType.Constant constant = DatabaseType.Constant.MYSQL;
//        String sql = "SET search_path TO 'public';";
//        String sql = "SET search_path='public','abc';";
        String sql = "SET a='public';";

        SQLFlexCheckEngine<CheckAuthParam, CheckAuthResult, SQLAuth> sqlFlexCheckEngine = new SQLFlexCheckEngine<>(sql, constant, null, "dbname", SQLAuth.class);
        assert sqlFlexCheckEngine.check(null).isSuccess();
    }

    @Test
    void rollback() {
        DatabaseType.Constant constant = DatabaseType.Constant.ORACLE;
        String sql = "rollback";
        SQLStatement test = test(constant, sql);

        SQLStatementContext context = new SQLContextEngine(null, "dbname", null).bind(test, null);

        AllowListAuth sqlAllowListAuth = SQLFlexCheckEngine.getChecker(AllowListAuth.class, constant);
        assert sqlAllowListAuth.check(context, null).isSuccess();
    }

    @Test
    void commit() {
        DatabaseType.Constant constant = DatabaseType.Constant.ORACLE;
        String sql = "commit";
        SQLStatement test = test(constant, sql);

        SQLStatementContext context = new SQLContextEngine(null, "dbname", null).bind(test, null);

        AllowListAuth sqlAllowListAuth = SQLFlexCheckEngine.getChecker(AllowListAuth.class, constant);
        assert sqlAllowListAuth.check(context, null).isSuccess();
    }

    @Test
    void slash() {
        DatabaseType.Constant constant = DatabaseType.Constant.ORACLE;
        String sql = "/";
        SQLStatement test = test(constant, sql);

        SQLStatementContext context = new SQLContextEngine(null, "dbname", null).bind(test, null);

        AllowListAuth sqlAllowListAuth = SQLFlexCheckEngine.getChecker(AllowListAuth.class, constant);
        assert sqlAllowListAuth.check(context, null).isSuccess();
    }

    @Test
    void beginTran() {
        DatabaseType.Constant constant = DatabaseType.Constant.SQL_SERVER;
//        String sql = "BEGIN TRAN;";
        String sql = "begin transaction;";
        SQLStatement test = test(constant, sql);

        SQLStatementContext context = new SQLContextEngine(null, "dbname", null).bind(test, null);

        AllowListAuth sqlAllowListAuth = SQLFlexCheckEngine.getChecker(AllowListAuth.class, constant);
        assert sqlAllowListAuth.check(context, null).isSuccess();
    }

    @Test
    void saveTran() {
        DatabaseType.Constant constant = DatabaseType.Constant.SQL_SERVER;
        String sql = "save transaction savepoint1;";
        SQLStatement test = test(constant, sql);

        SQLStatementContext context = new SQLContextEngine(null, "dbname", null).bind(test, null);

        AllowListAuth sqlAllowListAuth = SQLFlexCheckEngine.getChecker(AllowListAuth.class, constant);
        assert sqlAllowListAuth.check(context, null).isSuccess();
    }

    @Test
    void declare() {
        DatabaseType.Constant constant = DatabaseType.Constant.SQL_SERVER;
//        String sql = "DECLARE @myVariable INT;";
        String sql = "DECLARE @myString NVARCHAR(50) = 'Hello, SQL Server!';";
        SQLStatement test = test(constant, sql);

        SQLStatementContext context = new SQLContextEngine(null, "dbname", null).bind(test, null);

        AllowListAuth sqlAllowListAuth = SQLFlexCheckEngine.getChecker(AllowListAuth.class, constant);
        assert sqlAllowListAuth.check(context, null).isSuccess();
    }

    @Test
    void oracleDeclare() {
        DatabaseType.Constant constant = DatabaseType.Constant.ORACLE;
        String sql = "DECLARE\n" +
                "   v_outer_var VARCHAR2(20) := 'Outer Block';\n" +
                "BEGIN\n" +
                "   DBMS_OUTPUT.PUT_LINE(v_outer_var); -- 输出 Outer Block\n" +
                "   DECLARE\n" +
                "      v_inner_var VARCHAR2(20) := 'Inner Block';\n" +
                "   BEGIN\n" +
                "      DBMS_OUTPUT.PUT_LINE(v_inner_var); -- 输出 Inner Block\n" +
                "   END;\n" +
                "END;\n" +
                "/";
        SQLStatement test = test(constant, sql);

        SQLStatementContext context = new SQLContextEngine(null, "dbname", null).bind(test, null);

        AllowListAuth sqlAllowListAuth = SQLFlexCheckEngine.getChecker(AllowListAuth.class, constant);
        assert !sqlAllowListAuth.check(context, null).isSuccess();
    }

}
