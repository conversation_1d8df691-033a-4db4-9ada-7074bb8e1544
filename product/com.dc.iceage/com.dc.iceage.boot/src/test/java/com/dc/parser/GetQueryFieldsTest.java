package com.dc.parser;

import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DPSqlParser;
import com.dc.sqlparser.types.EDbType;
import com.dc.summer.parser.sql.model.ColumnData;
import com.dc.summer.parser.utils.GetQueryFieldsUtil;
import com.dc.summer.parser.utils.SqlParserUtil;
import com.dc.summer.parser.utils.model.SqlParseResult;
import com.dc.type.DatabaseType;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetQueryFieldsTest {

    private static Map<EDbType, String> mapFile = new HashMap<>();

    static {
        mapFile.put(EDbType.dbvoracle, "oracle-3-1088行sql.sql");
        mapFile.put(EDbType.dbvmysql, "mysql-1.sql");

        mapFile.put(EDbType.dbvmssql, "sqlserver.txt");
        mapFile.put(EDbType.dbvdb2, "db2.txt");
        mapFile.put(EDbType.dbvpostgresql, "td-pg.txt");
    }

    public static void fileSqlTest() {
        EDbType dbType = EDbType.dbvoracle;
        String path = mapFile.get(dbType);
        DPSqlParser parser = new DPSqlParser(dbType);

        try {
            InputStream resourceAsStream = GetQueryFieldsTest.class.getClassLoader().getResourceAsStream("longSelect"+ File.separator + path);
            if (resourceAsStream != null) {
                StringBuilder stringBuilder = new StringBuilder();
                try (BufferedReader br = new BufferedReader(new InputStreamReader(resourceAsStream))) {
                    String sql = null;
                    while ((sql = br.readLine()) != null) {
                        if (sql.isEmpty()) {
                            continue;
                        }

                        stringBuilder.append(sql).append("\n");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                parser.setSqltext(stringBuilder.toString());
                int ret = parser.parse();
                DCustomSqlStatement statement = parser.sqlstatements.get(0);
                GetQueryFieldsUtil getQueryFieldsUtil = new GetQueryFieldsUtil();
                List<ColumnData> object = getQueryFieldsUtil.getColumnDataList(statement);
                System.out.println(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void simpleSqlTest() {
        String sql;
        sql = "SELECT * FROM (SELECT cy3.*,cy2.* FROM \"CY3\",cy2);";
        sql = "SELECT * FROM (SELECT id as id1, name as name1 FROM \"CY3\");";
        sql = "select count(*) from aaa";
        sql = "SELECT a.* FROM (SELECT id as id1, name as name1 FROM \"CY3\") a;";
        sql = "SELECT a.* FROM (SELECT cy3.*,cy2.* FROM \"CY3\",cy2) a;";
        sql = "select a.password || a.age || name from tab a;";
        sql = "select password - 9 from SCOTT.tab;";
        sql = "select userid,phone,username, concat(password, ' ') || concat(password, ' ') from SCOTT.tab;";
        sql = "select 'aaa', b from bbb";
        sql = "select b from (select id+name as b from aaa)";
        sql = "select a.bizid from (select bizid from bizholder) b, arcdat.bizmain a";
        sql = " select (\n" +
                "    select\n" +
                "      wm_concat (name)\n" +
                "    from\n" +
                "      arcdat.bizholder h\n" +
                "    where\n" +
                "      a.bizid = h.bizid\n" +
                "      and h.peopletype = 1006\n" +
                "      and h.orgtype in (1, 2)\n" +
                "  ) name\n" +
                "\n" +
                "  from\n" +
                "  arcdat.bizmain a";
        sql = "select (\n" +
                "           select\n" +
                "               wm_concat (name)\n" +
                "           from\n" +
                "               arcdat.bizholder h\n" +
                "       ) name\n" +
                "\n" +
                "from\n" +
                "    arcdat.bizmain a;"; // todo 有问题
        List<SqlParseResult> parser = SqlParserUtil.parser(sql, DatabaseType.ORACLE.getValue());
        DCustomSqlStatement dCustomSqlStatement = parser.get(0).gettCustomSqlStatement();
        GetQueryFieldsUtil getQueryFieldsUtil = new GetQueryFieldsUtil();
        List<ColumnData> object = getQueryFieldsUtil.getColumnDataList(dCustomSqlStatement);
        System.out.println(object);
    }

    public static void main(String[] args) {

        fileSqlTest();
//        simpleSqlTest();

    }
}
