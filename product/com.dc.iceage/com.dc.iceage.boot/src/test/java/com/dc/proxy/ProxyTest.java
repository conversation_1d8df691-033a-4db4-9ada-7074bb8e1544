package com.dc.proxy;


import com.dc.IceagePeApplication;
import com.dc.proxy.model.data.message.HiveMetaDataMessage;
import com.dc.springboot.core.model.database.*;
import com.dc.springboot.core.model.execution.SqlListExecuteMessage;
import com.dc.test.springboot.BaseSpringBootTest;
import com.dc.test.springboot.DataBaseTest;
import com.dc.type.DatabaseType;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;


@SpringBootTest(classes = IceagePeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ProxyTest extends BaseSpringBootTest {


    @BeforeEach
    public void init() throws Exception {

        setupMockMvc();
        setupDataBase();

    }

    @Override
    protected List<DatabaseType> selectedDatabase() {
        return List.of(DatabaseType.MYSQL, DatabaseType.PG_SQL);
    }

    @Test
    void execute() {
        test(dataBaseTest -> {
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            SqlListExecuteMessage message = new SqlListExecuteMessage();
            ConnectionMessage connectionMessage = new ConnectionMessage();
            connectionMessage.setConnectionConfig(connectionConfig);
            message.setConnectionConfig(connectionConfig);
            message.setSqlList(dataBaseTest.getSqlList());
            post("/internal/execute", message);
        });
    }

    @Test
    void explain() {
        test(dataBaseTest -> {
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            if (DatabaseType.DM.getValue().equals(connectionConfig.getDatabaseType())) {
                SqlListExecuteMessage message = new SqlListExecuteMessage();
                ConnectionMessage connectionMessage = new ConnectionMessage();
                connectionMessage.setConnectionConfig(connectionConfig);
                message.setConnectionConfig(connectionConfig);
                message.setSqlList(dataBaseTest.getSqlList());
                post("/internal/explain", message);
            }
        });
    }

    @Test
    void metadata() {
        test(dataBaseTest -> {
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            if (DatabaseType.HIVE.getValue().equals(connectionConfig.getDatabaseType()) ||
                    DatabaseType.IMPALA.getValue().equals(connectionConfig.getDatabaseType()) ||
                    DatabaseType.INCEPTOR.getValue().equals(connectionConfig.getDatabaseType()) ||
                    DatabaseType.HETU.getValue().equals(connectionConfig.getDatabaseType())) {
                HiveMetaDataMessage message = new HiveMetaDataMessage();
                message.setConnectionConfig(connectionConfig);
            }
        });
    }

    @Test
    void testConnection() {
        test(dataBaseTest -> {
            SqlListExecuteMessage message = new SqlListExecuteMessage();
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            message.setConnectionConfig(connectionConfig);
            String testSql = connectionConfig.getTestSql();
            message.setSqlList(List.of(StringUtils.isNotBlank(testSql) ? testSql : "select 1 from dual"));
            post("/internal/execute", message);
        });
    }

    @Test
    void testStructCompareTable() {
        test(dataBaseTest -> {
            StructCompareMessage message = getStructCompareMessage(dataBaseTestMap.get("mysql"), dataBaseTestMap.get("mysql"));
//            StructCompareMessage message = getStructCompareMessage(dataBaseTestMap.get("mysql"), dataBaseTestMap.get("pg"));

            List<StructCompareObjectMapping> list = new ArrayList<>();
            list.add(new StructCompareObjectMapping(StructCompareObjectType.TABLE, "actor", "actor1"));
            list.add(new StructCompareObjectMapping(StructCompareObjectType.TABLE, "actor", null));

            message.setObjectMappings(list);

            post("/internal/struct-compare", message);
        });
    }

    @Test
    void testStructCompareSchema() {
//        StructCompareMessage message = getStructCompareMessage(dataBaseTestMap.get("mysql"), dataBaseTestMap.get("mysql"));
        StructCompareMessage message = getStructCompareMessage(dataBaseTestMap.get("mysql"), dataBaseTestMap.get("pg"));
        post("/internal/struct-compare", message);
    }

    @NotNull
    private static StructCompareMessage getStructCompareMessage(DataBaseTest dataBaseTest1, DataBaseTest dataBaseTest2) {
        StructCompareMessage message = new StructCompareMessage();
        ConnectionConfig source = new ConnectionConfig();
        BeanUtils.copyProperties(dataBaseTest1.getConnectionConfig(), source);
        source.setSchemaName("BOBO");
        ConnectionConfig target = new ConnectionConfig();
        BeanUtils.copyProperties(dataBaseTest2.getConnectionConfig(), target);
//        target.setSchemaName("test"); // pg
        target.setSchemaName("a1"); // mysql
        message.setSourceConnectionConfig(source);
        message.setTargetConnectionConfig(target);
        message.setConfigTypes(List.of(StructCompareConfigType.TABLE, StructCompareConfigType.COLUMNS, StructCompareConfigType.PRIMARY_KEY, StructCompareConfigType.FOREIGN_KEY, StructCompareConfigType.UNIQUE_CONSTRAINT, StructCompareConfigType.INDEX));
        return message;
    }

}
