package com.dc.parser;

import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DPSqlParser;
import com.dc.sqlparser.nodes.TParseTreeNode;
import com.dc.sqlparser.types.EDbType;
import com.dc.summer.parser.utils.SqlParserUtil;
import com.dc.summer.parser.utils.model.SqlParseResult;
import com.dc.type.DatabaseType;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DPSqlParserTest {

    private static Map<EDbType, String> mapFile = new HashMap<>();

    static {
        mapFile.put(EDbType.dbvmysql, "mysql.txt");
        mapFile.put(EDbType.dbvoracle, "oracle.txt");
        mapFile.put(EDbType.dbvmssql, "sqlserver.txt");
        mapFile.put(EDbType.dbvdb2, "db2.txt");
        mapFile.put(EDbType.dbvpostgresql, "td-pg.txt");
    }

    public static void batchSqlTest() {
        EDbType dbType = EDbType.dbvoracle;
//        String path = mapFile.get(dbType);
        String path = "create_package.sql";
        DPSqlParser parser = new DPSqlParser(dbType);

        try {
            InputStream resourceAsStream = DPSqlParserTest.class.getClassLoader().getResourceAsStream("sql"+ File.separator + path);
            if (resourceAsStream != null) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(resourceAsStream))) {
                    String sql = null;
                    while ((sql = br.readLine()) != null) {
                        if (sql.isEmpty()) {
                            continue;
                        }

                        parser.setSqltext(sql);
                        int ret = parser.parse();
                        if (ret == 0) {
                            System.out.println("解析成功");
                            DCustomSqlStatement statement = parser.sqlstatements.get(0);
                            System.out.println(statement.toString());
                        } else {
                            System.out.println("解析失败");
                            System.out.println("错误信息：" + parser.getErrormessage());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void test() {
//        String path = mapFile.get(dbType);
        String path = "create_package.sql";

        try {
            InputStream resourceAsStream = DPSqlParserTest.class.getClassLoader().getResourceAsStream("sql"+ File.separator + path);
            if (resourceAsStream != null) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(resourceAsStream))) {
                    StringBuilder sb = new StringBuilder();
                    String sql = null;
                    while ((sql = br.readLine()) != null) {
                        if (sql.isEmpty()) {
                            continue;
                        }
                        sb.append(sql);
                        System.out.println(sql);
                    }
                    List<SqlParseResult> parser = SqlParserUtil.parser(sb.toString(), 1);
                    TParseTreeNode treeNode = (TParseTreeNode) parser.get(0).gettCustomSqlStatement();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void singleSqlTest() {
        DPSqlParser parser = new DPSqlParser(EDbType.dbvmysql);
        parser.sqltext = "CREATE PROCEDURE `generate_entity_data`(IN num_rows INT)\n" +
                "BEGIN\n" +
                "    DECLARE i INT DEFAULT 1;\n" +
                "\t\tSTART TRANSACTION;\n" +
                "\t\tSET FOREIGN_KEY_CHECKS = 0;\n" +
                "\t\tWHILE i <= num_rows DO\n" +
                "\t\t\t\tINSERT INTO `dc_db_entity` (`db_type`, `entity_name`) VALUES (1, uuid());\n" +
                "\t\t\t\tSET i = i + 1;\n" +
                "\t\tEND WHILE;\n" +
                "\t\tSET FOREIGN_KEY_CHECKS = 1;\n" +
                "\t\tCOMMIT;\n" +
                "END;";
        int ret = parser.parse();
        if (ret == 0) {
            System.out.println("解析成功");
            DCustomSqlStatement statement = parser.sqlstatements.get(0);
            System.out.println(statement.toString());
        } else {
            System.out.println("解析失败");
            System.out.println("错误信息：" + parser.getErrormessage());
        }
    }

    public static void main(String[] args) {

//        batchSqlTest();
//        singleSqlTest();
        test();

    }
}
