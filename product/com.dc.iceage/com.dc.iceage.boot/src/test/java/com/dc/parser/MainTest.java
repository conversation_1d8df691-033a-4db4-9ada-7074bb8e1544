package com.dc.parser;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MainTest {

    public static void main(String[] args) {
        String string = "[a.[aaa]";
        List<String> strings = parseNames(string);
        for (String s : strings) {
            System.out.println(s);
        }
        /*if (string.startsWith("[") && string.endsWith("]")) {
            System.out.println(string.substring(1, string.length() - 1));
        }*/
    }

    public static List<String> parseNames(String nameString) {
        String name = nameString.trim();
        List<String> names = new ArrayList<>();
        String[] splits = nameString.split("\\.");
        if ((name.startsWith("\"") && name.endsWith("\""))
                || (name.startsWith("[") && name.endsWith("]"))) {
            for (int i = 0; i < splits.length; i++) {
                String split = splits[i].trim();
                if (split.startsWith("[") && !split.endsWith("]")) {
                    StringBuilder buffer = new StringBuilder();
                    buffer.append(splits[i]);
                    while (!(split = splits[++i].trim()).endsWith("]")) {
                        buffer.append(".");
                        buffer.append(splits[i]);
                    }

                    buffer.append(".");
                    buffer.append(splits[i]);

                    names.add(buffer.toString());
                    continue;
                }
                if (split.startsWith("\"") && !split.endsWith("\"")) {
                    StringBuilder buffer = new StringBuilder();
                    buffer.append(splits[i]);
                    while (!(split = splits[++i].trim()).endsWith("\"")) {
                        buffer.append(".");
                        buffer.append(splits[i]);
                    }

                    buffer.append(".");
                    buffer.append(splits[i]);

                    names.add(buffer.toString());
                    continue;
                }
                names.add(splits[i]);
            }
        } else {
            names.addAll(Arrays.asList(splits));
        }
        return names;
    }
}
