package com.dc.parser.exec.sql;

public class ORACLE {
    // 表建议使用主键（平安）
    public static String create_table = "CREATE TABLE tablename (\n" +
            "            name char(21),\n" +
            "            update_time timestamp,\n" +
            "            id int PRIMARY KEY\n" +
            "            ) TABLESPACE sYsteM;";
    public static String alter_table_column = "ALTER TABLE table_name\n" +
            "ADD (\n" +
            "    blob_column_name BLOB\n" +
            ");";

    //外键必须有索引（平安）
    public static String create_table_foreign_index = "CREATE TABLE child_table (\n" +
            "    child_id NUMBER PRIMARY KEY,\n" +
            "    parent_id NUMBER,\n" +
            "    CONSTRAINT fk_child_parent\n" +
            "        FOREIGN KEY (parent_id)\n" +
            "       REFERENCES parent_table(parent_id)\n" +
            "       DEFERRABLE USING INDEX indexname -- 判断 FOREIGN KEY 后是否有 USING INDEX\n" +
            ");";
    public static String alter_table_foreign_index = "alter table asd add  CONSTRAINT constraintName FOREIGN KEY (columnNames) REFERENCES tableName DEFERRABLE USING INDEX indexname;";

    // 分区列不允许为空（平安）
    public static String create_table_partition = "   CREATE TABLE sales (\n" +
            "       sale_date DATE NOT NULL\n" +
            "   ) PARTITION BY RANGE (sale_date) ( -- 判断 sale_date 列是否有 NOT NULL\n" +
            "       PARTITION sales_q1 VALUES LESS THAN (TO_DATE('01-APR-2024', 'DD-MON-YYYY')),\n" +
            "       PARTITION sales_q2 VALUES LESS THAN (TO_DATE('01-JUL-2024', 'DD-MON-YYYY'))\n" +
            "   );";

    // 序列建议设置cache值
    public static String create_sequence = "CREATE SEQUENCE sequence_name\n" +
            "    START WITH 1\n" +
            "    CACHE 2;";

    // 创建索引必须初始化事务槽（平安）
    public static String create_index = "CREATE INDEX index_name ON table_name (column_name) INITRANS 2; -- 判断是否有 INITRANS 值";
    public static String create_index_tablespace = "CREATE INDEX idx_vehiclepass_com3 \n" +
            "ON traffic_vehicle_pass(plate_no, pass_time, crossing_id) TABLESPACE sys;  --为索引指定表空间";
    public static String create_index_composite = "CREATE INDEX idx_lastname_dept ON employees(last_name, department_id);";
    public static String create_index_cluster = "CREATE INDEX index_name ON CLUSTER cluster_name INITRANS 4;";

    public static String create_view = "CREATE OR REPLACE VIEW avg_salary_per_dept AS\n" +
            "SELECT d.department_name, AVG(e.salary) AS average_salary\n" +
            "FROM employees e\n" +
            "JOIN departments d ON e.department_id = d.department_id\n" +
            "GROUP BY d.department_name;";

    public static String create_materialized_view = "CREATE MATERIALIZED VIEW mv_example\n" +
            "REFRESH COMPLETE ON DEMAND\n" +
            "AS\n" +
            "SELECT column1, column2\n" +
            "FROM table1\n" +
            "JOIN table2 ON table1.id = table2.table1_id\n" +
            "WHERE some_condition;";

    public static String create_trigger = "CREATE OR REPLACE TRIGGER my_table_before_insert\n" +
            "BEFORE INSERT ON my_table\n" +
            "FOR EACH ROW\n" +
            "BEGIN\n" +
            "  -- 在这里编写触发器要执行的PL/SQL代码\n" +
            "  -- :NEW 表示新插入的行\n" +
            "  :NEW.created_at := SYSDATE; -- 假设my_table有一个created_at字段\n" +
            "END;\n" +
            "/";

    public static String create_procedure = "CREATE OR REPLACE PROCEDURE add_numbers (\n" +
            "    num1 IN NUMBER,\n" +
            "    num2 IN NUMBER\n" +
            ") AS\n" +
            "    result NUMBER;\n" +
            "BEGIN\n" +
            "    result := num1 + num2;\n" +
            "    DBMS_OUTPUT.PUT_LINE('The result is: ' || TO_CHAR(result));\n" +
            "END;\n" +
            "/";

    public static String drop_table_column = "ALTER TABLE table_name DROP COLUMN column_name CASCADE CONSTRAINTS;\n";

    public static String modify_table_column = "ALTER TABLE employees MODIFY age NUMBER(3,1);";

    public static String insert = "INSERT /*+ hint_comment */ INTO employees (employee_id, first_name, last_name, hire_date)\n" +
            "VALUES (101, 'John', 'Doe', DATE '2023-04-01');";

    public static String insert_no_column = "INSERT  INTO employees \n" +
            "VALUES (101, 'John', 'Doe', DATE '2023-04-01');";

    public static String insert_all = "INSERT ALL \n" +
            "    INTO employees (employee_id, first_name, last_name, hire_date) VALUES (103, 'Alice', 'Johnson', DATE '2023-06-9')\n" +
            "    INTO employees (employee_id, first_name, last_name, hire_date) VALUES (104, 'Bob', 'Williams', DATE '2023-07-1')\n" +
            "SELECT 1 FROM DUAL;\n";

    public static String insert_all_no_column = "INSERT ALL \n" +
            "    INTO employees (employee_id, first_name, last_name, hire_date) VALUES (103, 'Alice', 'Johnson', DATE '2023-06-9')\n" +
            "    INTO employees (employee_id, first_name, last_name, hire_date) VALUES (104, 'Bob', 'Williams', DATE '2023-07-1')\n" +
            "SELECT 1 FROM DUAL;\n";

    public static String select = "SELECT /*+ hint_comment */ * FROM employees where 1 = 1  ORDER BY salary DESC, department_id ASC;";

    public static String delete = "DELETE /*+ hint_comment */ FROM employees WHERE employee_id = 101;";
    public static String update = "UPDATE /*+ hint_comment */ employees SET salary = salary * 1.1 WHERE department_id = 50;";

    public static String merge = "MERGE /*+ hint_comment */ INTO your_table yt\n" +
            "USING (\n" +
            "    SELECT id, column_to_update, ROW_NUMBER() OVER (ORDER BY date_created) as rn\n" +
            "    FROM your_table\n" +
            "    WHERE some_condition\n" +
            ") subquery\n" +
            "ON (yt.id = subquery.id)\n" +
            "WHEN MATCHED THEN UPDATE SET yt.column_to_update = column_to_update;";

    public static String where = "SELECT * FROM employees WHERE department_id != \"ggg\" AND salary > 5000 and id <> 2 and 4 = 4 or id is not null " +
            "and name in (1,2,3,a(b),5);";

    public static String select_join_on = "SELECT e.employee_id employee_id, e.first_name, e.last_name, d.department_name\n" +
            "FROM employees e\n" +
            "JOIN departments d ON e.department_id = d.department_id\n" +
            "JOIN departments2 d2 ON e.department_id = d2.department_id\n" +
            "WHERE e.salary > 5000;";

    public static String select_having = "SELECT Customer customer,SUM(OrderPrice) " +
            "FROM Orders GROUP BY Customer HAVING SUM(OrderPrice)<2000";

    public static String select_for_update = "select * from aaa where 1.1=2 for update ";

    public static String select_union = "SELECT employee_id, first_name, last_name\n" +
            "FROM employees1 aa where 1=1\n" +
            "UNION\n" +
            "SELECT employee_id, first_name, last_name\n" +
            "FROM employees2 aa \n" +
            "UNION\n" +
            "SELECT employee_id, first_name, last_name\n" +
            "FROM employees3 bb\n" +
            "WHERE department_id = 50;";

    public static String select_union_all = "SELECT * FROM \"QYYTEST1\".\"AAA1\" WHERE department_id = 50\n" +
            "union\n" +
            "SELECT * FROM \"QYYTEST1\".\"AAA2\" WHERE department_id = 50\n" +
            "union all\n" +
            "SELECT * FROM \"QYYTEST1\".\"AAA2\"";

    public static String scalar_select = "SELECT * FROM DUAL where id in (SELECT * FROM tablename);";
    public static String scalar_select2 = "SELECT * FROM DUAL where id > (SELECT * FROM tablename);";

    public static String fuzzy_select = "SELECT * FROM employees\n" +
            "WHERE first_name LIKE '%A%'\n" +
            "AND first_name LIKE '%B%';";

    public static String not_select = "SELECT column1, column2\n" +
            "FROM table_name\n" +
            "WHERE column1 NOT BETWEEN value1 AND value2;";

    public static String not_select2 = "SELECT column1, column2\n" +
            "FROM table_name\n" +
            "WHERE column1 != 'specific_value';";

    public static String not_select3 = "SELECT column1, column2\n" +
            "FROM table_name\n" +
            "WHERE column1 <> 'specific_value';";

    public static String scalar_select3 = "SELECT d.department_name,\n" +
            "       (SELECT e.last_name\n" +
            "        FROM employees e\n" +
            "        WHERE e.department_id = d.department_id\n" +
            "        ORDER BY e.salary DESC\n" +
            "        FETCH FIRST 1 ROW ONLY) AS highest_paid_employee\n" +
            "FROM departments d;";

    public static String scalar_select4 = "SELECT last_name, salary\n" +
            "FROM employees\n" +
            "WHERE salary > (\n" +
            "    SELECT AVG(salary)\n" +
            "    FROM employees\n" +
            "    WHERE job_id = 'SALES_REP'\n" +
            ")\n";

    public static String expr_in_where = "select name from stu where age + 1 < 12";

    public static String select_nest_loop = "select name from (select name from (select * from aaa) ) stu where age + 1 < 12";

    public static String alter_table_rename = "alter table tab1 rename to \"ghhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh\";\n";
    public static String alter_table_rename_add = "ALTER TABLE employees ADD salarywwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww decimal(10,2);\n";
    public static String alter_column_rename = "ALTER TABLE employees RENAME COLUMN emp_name TO ghghhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh;";

    public static String alter_table_rename_simple = "alter table tab1 rename to AAA;";
    public static String alter_table_rename_simple_quote = "alter table tab1 rename to \"aaa\";";

    public static String alter_column_rename_simple_add = "ALTER TABLE employees ADD (big1 char(21));";
    public static String alter_column_rename_simple = "ALTER TABLE employees RENAME COLUMN emp_name TO EMP1;";
    public static String alter_column_modify = "ALTER TABLE employees MODIFY (salary char(20));";

    public static String alter_index_rename = "alter index aaa rename to bbb";
    public static String select_not_exists = "select * from DEPARTMENTS  D where not EXISTS (select 1 from EMPLOYEES E where D.DEPARTMENT_ID = E.DEPARTMENT_ID)";
    public static String create_table_as_select = "create table t1 as select * from t2 where 1=1;";
    public static String alter_table_drop_primary = "alter table cy1 drop primary key\n";
    public static String alter_table_modify_partition_column = "alter table \"ZSS\".\"FENQUQQQ\"  modify id1 not null";

}
