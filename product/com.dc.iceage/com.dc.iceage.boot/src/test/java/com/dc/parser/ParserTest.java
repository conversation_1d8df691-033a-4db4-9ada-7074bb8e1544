package com.dc.parser;

import com.dc.IceagePeApplication;
import com.dc.iceage.config.GlobalConfig;
import com.dc.parser.type.ConnectionModeType;
import com.dc.repository.mysql.mapper.SchemaMapper;
import com.dc.repository.mysql.model.Schema;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.model.parser.ParserCacheMessage;
import com.dc.springboot.core.model.parser.ParserParamDto;
import com.dc.summer.registry.center.Global;
import com.dc.test.springboot.BaseSpringBootTest;
import com.dc.type.DatabaseType;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = IceagePeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class ParserTest extends BaseSpringBootTest {

    @Resource
    private JSON json;

    @Resource
    private SchemaMapper schemaMapper;

    @Override
    protected List<DatabaseType> selectedDatabase() {
        return List.of(DatabaseType.ORACLE);
    }

    @BeforeEach
    public void setupGlobal() {
        GlobalConfig globalConfig = applicationContext.getBean(GlobalConfig.class);
        Global.setConfig(globalConfig);
    }

    @Order(0)
    @Test
    void testH2() {
        List<Schema> schemasByNameIgnoreCase = schemaMapper.getSchemasByNameIgnoreCase("123", "catalogName", "schemaName");
        System.out.println(schemasByNameIgnoreCase.size());
    }

    @Order(1)
    @Test
    void preCheckSql() {
        boolean isAdmin = true;
        boolean isExecute = false;
        ParserParamDto message = getPreCheckParamDTO(isAdmin, isExecute);
        post("/sql/preCheckSql", message);
    }

    @Order(2)
    @Test
    void executeSql() {
        boolean isAdmin = true;
        boolean isExecute = true;
        ParserParamDto message = getPreCheckParamDTO(isAdmin, isExecute);
        post("/sql/executeSql", message);
    }

    @Order(3)
    @Test
    void preCheckSqlBatch() throws JsonProcessingException {
        boolean isExecute = false;
        ParserCacheMessage message = getPreCheckCacheDTO(isExecute);
        post("/sql/preCheckSqlBatch", message);
    }

    @Order(4)
    @Test
    void executeSqlBatch() throws JsonProcessingException {
        boolean isExecute = true;
        ParserCacheMessage message = getPreCheckCacheDTO(isExecute);
        post("/sql/executeSqlBatch", message);
    }

    private ParserCacheMessage getPreCheckCacheDTO(boolean isExecute) throws JsonProcessingException {
        ParserCacheMessage cacheDTO = buildNormalPreCheckCacheDTO(isExecute);

        switch (selectedDatabase().get(0)) {
            case ORACLE:
                buildOraclePreCheckCacheDTO(cacheDTO);
                break;
            case MYSQL:
                buildMysqlPreCheckCacheDTO(cacheDTO);
                break;
            default:
                break;
        }

        return cacheDTO;
    }

    private void buildOraclePreCheckCacheDTO(ParserCacheMessage cacheDTO) throws JsonProcessingException {
        cacheDTO.setToken("sql_console_8067aa58-f61e-4f04-8ffd-c8aa66c96fe5");
        cacheDTO.setSqlList(listToString(List.of("SELECT * FROM \"QYYTEST1\".\"CYTEST1\";", "SELECT * FROM \"QYYTEST1\".\"CYTEST2\";")));
    }

    private void buildMysqlPreCheckCacheDTO(ParserCacheMessage cacheDTO) {
        cacheDTO.setToken("");
        cacheDTO.setSqlList("");
    }

    private String listToString(List<String> sqlList) throws JsonProcessingException {
        return json.getObjectMapper().writeValueAsString(sqlList);
    }

    private ParserCacheMessage buildNormalPreCheckCacheDTO(boolean isExecute) {
        ParserCacheMessage cacheDTO = new ParserCacheMessage();
        if (isExecute) {
            cacheDTO.setIsVerify("SqlAuthVerify,SqlAccessControl,SqlAffectedRows,SqlExecuteLevel,DataMaskParser,DataRowIsEdit");
        } else {
            cacheDTO.setIsVerify("SqlBackup");
        }
        cacheDTO.setUseDatabase("");
        cacheDTO.setCatalogName("");
        cacheDTO.setRecycleBinSql(null);
        cacheDTO.setLimit(1000);
        cacheDTO.setOffset(0);
        cacheDTO.setOrigin(1L);
        cacheDTO.setRequestTime(null);
        cacheDTO.setPreOperation(null);
        cacheDTO.setObjectType(null);
        return cacheDTO;
    }

    private ParserParamDto getPreCheckParamDTO(boolean isAdmin, boolean isExecute) {
        ParserParamDto paramDTO = buildNormalPreCheckParamDTO(isAdmin, isExecute);

        switch (selectedDatabase().get(0)) {
            case ORACLE:
                buildOraclePreCheckParamDTO(paramDTO);
                break;
            case MYSQL:
                buildMysqlPreCheckParamDTO(paramDTO);
                break;
            case ELASTIC_SEARCH:
                buildElasticSearchPreCheckParamDTO(paramDTO);
                break;
            default:
                break;
        }

        return paramDTO;
    }

    private void buildOraclePreCheckParamDTO(ParserParamDto paramDTO) {
        paramDTO.setSql("SELECT * FROM \"CYUSER1\".\"CY1\"");
        paramDTO.setDbType(DatabaseType.ORACLE.getValue());
        paramDTO.setConnectId("4bbf2a31b2ee40cf80f3bb1087dae2d1"); // 161-system
        paramDTO.setSchemaId("9d61a656c4244fad88ecc0f144fba0a2"); // CYUSER1
    }

    private void buildMysqlPreCheckParamDTO(ParserParamDto paramDTO) {
        paramDTO.setSql("SELECT * FROM `cyschema1`.`employees`");
        paramDTO.setDbType(DatabaseType.MYSQL.getValue());
        paramDTO.setConnectId("c5ccac108777433db9c503542db53763"); // 161-system
        paramDTO.setSchemaId("5e7b50116e9e48998ce0a57bb7fa24ba"); // CYUSER1
    }

    private void buildElasticSearchPreCheckParamDTO(ParserParamDto paramDTO) {
        paramDTO.setSql("GET /auth_history_202208/_search\n" +
                "{\n" +
                "    \"from\": 0,\n" +
                "    \"size\": 1000\n" +
                "}");
        paramDTO.setDbType(DatabaseType.ELASTIC_SEARCH.getValue());
        paramDTO.setConnectId("937063efd74077f3a0eaf183ae6a09fa"); // 192.168.3.127:9200
        paramDTO.setSchemaId("**************"); // _TMP_ES_SCHEMA
    }

    private ParserParamDto buildNormalPreCheckParamDTO(boolean isAdmin, boolean isExecute) {
        ParserParamDto paramDTO = new ParserParamDto();
        paramDTO.setLimit(1000);
        paramDTO.setOffset(0);
        paramDTO.setOrigin(1L);
        paramDTO.setUserId(getUserId(isAdmin));
        paramDTO.setRequestTime(null);
        paramDTO.setUseDatabase("");
        paramDTO.setIsAccountConn(0L);
        paramDTO.setAccountId("0");
        paramDTO.setPreOperation(null);
        paramDTO.setEnableDesensiteType(1);
        paramDTO.setSymbol(null);
        paramDTO.setRecycleBinSql(null);
        paramDTO.setObjectType(null);
        paramDTO.setSqlList(null);
        paramDTO.setCurrentPattern(ConnectionModeType.SECURITY_COOPERATE.getValue());
        paramDTO.setAccount("");
        paramDTO.setCatalogName("");
        if (isExecute) {
            paramDTO.setIsVerify("SqlAuthVerify,SqlAccessControl,SqlAffectedRows,SqlExecuteLevel,DataMaskParser,DataRowIsEdit");
        } else {
            paramDTO.setIsVerify("SqlBackup");
        }
        paramDTO.setOrderMaskLevel(null);
        return paramDTO;
    }

    private String getUserId(boolean admin) {
        return admin ? "edbcc7ecdad44765bd1906e7a65fa9c8" : "";
    }

    @SneakyThrows
    protected ResultActions post(String path, Object message) {
        return mockMvc.perform(MockMvcRequestBuilders.post(path)
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(json.getObjectMapper().writeValueAsString(message)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print());
    }

}
