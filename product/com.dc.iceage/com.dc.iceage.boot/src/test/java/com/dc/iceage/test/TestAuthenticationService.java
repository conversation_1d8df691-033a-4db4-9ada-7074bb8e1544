package com.dc.iceage.test;

import com.pingan.cdsf.driver.bridger.dto.AuthenticationCyberarkDatabaseDto;
import com.pingan.cdsf.driver.bridger.dto.ResponseDto;
import com.pingan.cdsf.driver.bridger.service.AuthenticationService;
import org.springframework.stereotype.Service;

@Service
public class TestAuthenticationService implements AuthenticationService {

    @Override
    public ResponseDto<String> getConnectionUserPassword(AuthenticationCyberarkDatabaseDto authenticationCyberarkDatabaseDto) {
        return new ResponseDto<>("", "", "whdata");
    }

    @Override
    public ResponseDto<String> getDamsConnectionUserPassword(AuthenticationCyberarkDatabaseDto authenticationCyberarkDatabaseDto) {
        return null;
    }
}
