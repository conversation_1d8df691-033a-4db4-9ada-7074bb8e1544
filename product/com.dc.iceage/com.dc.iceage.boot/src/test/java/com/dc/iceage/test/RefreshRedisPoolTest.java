package com.dc.iceage.test;

import com.dc.IceagePeApplication;
import com.dc.proxy.service.ExecuteService;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.ConnectionMessage;
import com.dc.springboot.core.model.execution.SqlListExecuteMessage;
import com.dc.test.springboot.BaseSpringBootTest;
import com.dc.type.DatabaseType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = IceagePeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class RefreshRedisPoolTest extends BaseSpringBootTest {

    @BeforeEach
    public void init() {
        setupMockMvc();
        setupDataBase();
    }

    @Override
    protected List<DatabaseType> selectedDatabase() {
        return List.of(DatabaseType.REDIS, DatabaseType.MONGODB);
    }


    @Resource
    private ExecuteService executeService;

    @Test
    void test() {
        test(dataBaseTest -> {
            ConnectionConfig connectionConfig = dataBaseTest.getConnectionConfig();
            connectionConfig.setAuthSource(1);
            SqlListExecuteMessage message = new SqlListExecuteMessage();
            ConnectionMessage connectionMessage = new ConnectionMessage();
            connectionMessage.setConnectionConfig(connectionConfig);
            message.setConnectionConfig(connectionConfig);
            message.setSqlList(dataBaseTest.getSqlList());

            executeService.execute(message);

            message.getConnectionConfig().getConnectionConfiguration().setUserPassword("123");

            executeService.execute(message);
        });
    }
}
