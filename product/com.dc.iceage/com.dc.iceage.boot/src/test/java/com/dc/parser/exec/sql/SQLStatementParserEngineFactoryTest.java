
package com.dc.parser.exec.sql;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.engine.CacheOption;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotSame;
import static org.junit.jupiter.api.Assertions.assertSame;

class SQLStatementParserEngineFactoryTest {

    DatabaseType databaseType = TypedSPILoader.getService(DatabaseType.class, DatabaseType.Constant.MYSQL);
    
    @Test
    void assertGetSQLStatementParserEngineNotSame() {
        SQLStatementParserEngine before = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(64, 1024L));
        SQLStatementParserEngine after = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(128, 1024L));
        assertNotSame(before, after);
    }
    
    @Test
    void assertGetSQLStatementParserEngineSame() {
        SQLStatementParserEngine before = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(128, 1024L));
        SQLStatementParserEngine after = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(128, 1024L));
        assertSame(before, after);
    }
}
