package com.dc.parser;

import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DPSqlParser;
import com.dc.sqlparser.types.EDbType;
import com.dc.summer.parser.sql.model.ColumnData;
import com.dc.summer.parser.utils.GetColumnsUtil;
import com.dc.summer.parser.utils.GetQueryFieldsUtil;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

public class GetColumnsTest {

    private static Map<EDbType, String> mapFile = new HashMap<>();

    static {
        mapFile.put(EDbType.dbvoracle, "oracle-2.sql");
        mapFile.put(EDbType.dbvmysql, "mysql-1.sql");

        mapFile.put(EDbType.dbvmssql, "sqlserver.txt");
        mapFile.put(EDbType.dbvdb2, "db2.txt");
        mapFile.put(EDbType.dbvpostgresql, "td-pg.txt");
    }

    public static void fileSqlTest() {
        EDbType dbType = EDbType.dbvmysql;
        String path = mapFile.get(dbType);
        DPSqlParser parser = new DPSqlParser(dbType);

        try {
            InputStream resourceAsStream = GetColumnsTest.class.getClassLoader().getResourceAsStream("longSelect"+ File.separator + path);
            if (resourceAsStream != null) {
                StringBuilder stringBuilder = new StringBuilder();
                try (BufferedReader br = new BufferedReader(new InputStreamReader(resourceAsStream))) {
                    String sql = null;
                    while ((sql = br.readLine()) != null) {
                        if (sql.isEmpty()) {
                            continue;
                        }

                        stringBuilder.append(sql).append("\n");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                parser.setSqltext(stringBuilder.toString());
                int ret = parser.parse();
                DCustomSqlStatement statement = parser.sqlstatements.get(0);
                GetColumnsUtil getQueryFieldsUtil = new GetColumnsUtil();
                Object[] object = getQueryFieldsUtil.getObject(statement);
                Object o = object[0];
                System.out.println(o);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void simpleSqlTest() {
        String sql;
        sql = "select (\n" +
                "           select\n" +
                "               wm_concat (name)\n" +
                "           from\n" +
                "               arcdat.bizholder h\n" +
                "       ) name\n" +
                "\n" +
                "from\n" +
                "    arcdat.bizmain a;";
        DPSqlParser parser = new DPSqlParser(EDbType.dbvoracle);
        parser.sqltext = sql;
        int ret = parser.parse();
        DCustomSqlStatement statement = parser.sqlstatements.get(0);

        GetColumnsUtil getQueryFieldsUtil = new GetColumnsUtil();
        Object[] object = getQueryFieldsUtil.getObject(statement);
        Object o = object[1];
        System.out.println(o);
    }

    public static void main(String[] args) {

        fileSqlTest();
//        simpleSqlTest();

    }
}
