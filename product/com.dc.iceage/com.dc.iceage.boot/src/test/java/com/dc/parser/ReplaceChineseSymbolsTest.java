package com.dc.parser;

import com.dc.summer.parser.utils.SqlParserUtil;
import com.dc.type.DatabaseType;

import java.util.List;

public class ReplaceChineseSymbolsTest {

    public static void main(String[] args) {

        int i = 0;
        for (String sql : list) {
            if (++i != 6) {
//                continue;
            }
            System.out.println(SqlParserUtil.replaceChineseSymbols(null, sql, DatabaseType.ORACLE.getValue()));
        }

    }

    public static List<String> list = List.of("INSERT INTO \"CYUSER1\".\"CY5\" (\"ID\",\"NAME\") VALUES ('2'，'1''');"
            // 2
            , "INSERT INTO \"CYUSER1\".\"CY5\" (\"ID\",\"NAME\") VALUES ('2'，'1（）''（）');"
            // 3
            , "INSERT INTO \"CYUSER1\".\"CY5\" (\"ID\",\"NAME\") VALUES ('2','随着大数据、云计算时代的到来，信息系统和数据不断膨胀，数据安全管理成为数据时代的一个重要挑战和课题。最近数年，数据安全事故层出不穷，为社会敲响警钟。在数据时代，除了要进行足够的网络安全防护，防止外部入侵之外，防止内部人员或者外包服务人员在日常管理、升级维护、密码修改、数据共享、软件测试等过程中的误操作或者犯罪活动造成事故和影响，对单位最宝贵的数据资产进行严密防护，显得十分重要。\n" +
                    "\n" +
                    "针对数据重要性高、数据量大、使用用户多、容易遭受误操作和发生数据泄露、泄密的问题，开发一套全局统一的数据安全管理系统，对数据的访问、操作、共享、下载、日常管理、升级维护、密码修改等进行统一的认证管理和操作权限预审核，构建一个数据安全保护层，提高数据管理的便利性和安全性，预防发生数据误删操作和数据泄露事故')"
            // 4
            , "INSERT INTO \"CYUSER1\".\"CY5\" （\"ID\",\"NAME\") VALUES ('2'，'（随着）大数据、云计算时代的到来，信息系统和数据不断膨胀，数据安全管理成为数据时代的一个重要挑战和课题。最近数年，数据安全事故层出不穷，为社会敲响警钟。在数据时代，除了要进行足够的网络安全防护，防止外部入侵之外，防止内部人员或者外包服务人员在日常管理、升级维护、密码修改、数据共享、软件测试等过程中的误操作或者犯罪活动造成事故和影响，对单位最宝贵的数据资产进行严密防护，显得十分重要。\n" +
                    "\n" +
                    "针对数据重要性高、数据量大、使用用户多、容易遭受误操作和发生数据泄露、泄密的问题，开发一套全局统一的数据安全管理系统，对数据的访问、操作、共享、下载、日常管理、升级维护、密码修改等进行统一的认证管理和操作权限预审核，构建一个数据安全保护层，提高数据管理的便利性和安全性，预防发生数据误删操作和数据泄露事故')"
            // 5
            , "update table1 set name = '云计算时代的到来，信息系' where id = 1"
            // 6
            , "select * from aaa，bbb where name = '来，信息系'"

    );
}
