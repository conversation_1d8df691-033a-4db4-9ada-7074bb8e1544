package com.dc.parser.exec;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.exec.check.SQLRuleCheckEngine;
import com.dc.parser.exec.sql.ORACLE;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleContent;
import com.dc.parser.service.impl.SQLCheckExecutorImpl;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class CheckEngineTest {

    @Test
    void oracle() {
        String sql = ORACLE.alter_table_modify_partition_column;
        List<CheckResult> checkResultList = test(DatabaseType.Constant.ORACLE, sql);
        System.out.println(checkResultList);
    }

    public List<CheckResult> test(DatabaseType.Constant constant, String sql) {
        List<CheckResult> check = null;
        List<CheckRuleContent> checkRuleContents = buildSqlCheckRuleParamList();
        try {
            check = new SQLRuleCheckEngine(
                    sql,
                    constant,
                    new SQLCheckExecutorImpl(null),
                    checkRuleContents,
                    "test",
                    false)
                    .check();
        } catch (Exception e) {
            check = checkSyntaxError(checkRuleContents, CheckRuleDatabasePrefix.ORACLE_.getType());
        }
        return check;
    }

    public List<CheckRuleContent> buildSqlCheckRuleParamList() {
        List<CheckRuleContent> sqlCheckRuleParamList = new ArrayList<>();
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_PK_NOT_EXIST.getValue(), CheckRuleUniqueKey.DDL_CHECK_PK_NOT_EXIST.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_INDEX_COUNT.getValue(), CheckRuleUniqueKey.DDL_CHECK_INDEX_COUNT.getName(), "2"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_FK_INDEX.getValue(), CheckRuleUniqueKey.DDL_CHECK_FK_INDEX.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_DISABLE_FK.getValue(), CheckRuleUniqueKey.DDL_DISABLE_FK.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_LENGTH.getValue(), CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_LENGTH.getName(), "11"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COLUMN_NAME_LENGTH.getValue(), CheckRuleUniqueKey.DDL_CHECK_COLUMN_NAME_LENGTH.getName(), "9"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_IS_UPPER_AND_LOWER_LETTER_MIXED.getValue(), CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_IS_UPPER_AND_LOWER_LETTER_MIXED.getName(), "2"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COLUMN_NAME_IS_UPPER_AND_LOWER_LETTER_MIXED.getValue(), CheckRuleUniqueKey.DDL_CHECK_COLUMN_NAME_IS_UPPER_AND_LOWER_LETTER_MIXED.getName(), "1"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_USING_KEYWORD.getValue(), CheckRuleUniqueKey.DDL_CHECK_TABLE_NAME_USING_KEYWORD.getName(), "upper,child_id,child_table,aaa"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COLUMN_NAME_USING_KEYWORD.getValue(), CheckRuleUniqueKey.DDL_CHECK_COLUMN_NAME_USING_KEYWORD.getName(), "upper,child_id1,child_table,emp,big"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_PARTITION_COLUMN_NOT_NULL.getValue(), CheckRuleUniqueKey.DDL_CHECK_PARTITION_COLUMN_NOT_NULL.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_SEQUENCE_CACHE_NOT_ZERO.getValue(), CheckRuleUniqueKey.DDL_CHECK_SEQUENCE_CACHE_NOT_ZERO.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_SEQUENCE_CACHE_VALUE_SIZE.getValue(), CheckRuleUniqueKey.DDL_CHECK_SEQUENCE_CACHE_VALUE_SIZE.getName(), "3", "5"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COLUMN_SPECIFIED_TYPE_NOTICE.getValue(), CheckRuleUniqueKey.DDL_CHECK_COLUMN_SPECIFIED_TYPE_NOTICE.getName(), "int,varchar,blob1"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COLUMN_BLOB_NOTICE.getValue(), CheckRuleUniqueKey.DDL_CHECK_COLUMN_BLOB_NOTICE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_INDEX_INITIALIZE_TRANSACTION_SLOT.getValue(), CheckRuleUniqueKey.DDL_CHECK_INDEX_INITIALIZE_TRANSACTION_SLOT.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_UNIQUE_INDEX_PREFIX.getValue(), CheckRuleUniqueKey.DDL_CHECK_UNIQUE_INDEX_PREFIX.getName(), "ind"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_INDEX_PREFIX.getValue(), CheckRuleUniqueKey.DDL_CHECK_INDEX_PREFIX.getName(), "ind1"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_INDEX_MUST_TABLESPACE.getValue(), CheckRuleUniqueKey.DDL_CHECK_INDEX_MUST_TABLESPACE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_COMPOSITE_INDEX_MAX.getValue(), CheckRuleUniqueKey.DDL_CHECK_COMPOSITE_INDEX_MAX.getName(), "1"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_CREATE_VIEW.getValue(), CheckRuleUniqueKey.DDL_CHECK_CREATE_VIEW.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_CREATE_TRIGGER.getValue(), CheckRuleUniqueKey.DDL_CHECK_CREATE_TRIGGER.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_CREATE_PROCEDURE.getValue(), CheckRuleUniqueKey.DDL_CHECK_CREATE_PROCEDURE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_CHAR_LENGTH.getValue(), CheckRuleUniqueKey.DDL_CHECK_CHAR_LENGTH.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_HINT_DROP_COLUMN.getValue(), CheckRuleUniqueKey.DDL_HINT_DROP_COLUMN.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_HINT_MODIFY_COLUMN_DATA_TYPE.getValue(), CheckRuleUniqueKey.DDL_HINT_MODIFY_COLUMN_DATA_TYPE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_INSERT_COLUMNS_EXIST.getValue(), CheckRuleUniqueKey.DML_CHECK_INSERT_COLUMNS_EXIST.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_BATCH_INSERT_LISTS_MAX.getValue(), CheckRuleUniqueKey.DML_CHECK_BATCH_INSERT_LISTS_MAX.getName(), "2"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_WITH_ORDER_BY.getValue(), CheckRuleUniqueKey.DML_CHECK_WITH_ORDER_BY.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_SELECT_HAS_WHERE.getValue(), CheckRuleUniqueKey.DML_CHECK_SELECT_HAS_WHERE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_UPDATE_OR_DELETE_HAS_WHERE.getValue(), CheckRuleUniqueKey.DML_CHECK_UPDATE_OR_DELETE_HAS_WHERE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_SELECT_HINT.getValue(), CheckRuleUniqueKey.DML_CHECK_SELECT_HINT.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_NUMBER_OF_JOIN_TABLES.getValue(), CheckRuleUniqueKey.DML_CHECK_NUMBER_OF_JOIN_TABLES.getName(), "3"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_NOT_RECOMMEND_HAVING.getValue(), CheckRuleUniqueKey.DML_NOT_RECOMMEND_HAVING.getName(), "3"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_SELECT_FOR_UPDATE.getValue(), CheckRuleUniqueKey.DML_CHECK_SELECT_FOR_UPDATE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_NUMBER_OF_COLUMNS.getValue(), CheckRuleUniqueKey.DDL_CHECK_NUMBER_OF_COLUMNS.getName(), "2"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_IS_AFTER_UNION_DISTINCT.getValue(), CheckRuleUniqueKey.DML_CHECK_IS_AFTER_UNION_DISTINCT.getName(), "2"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_CREATE_TIME_COLUMN.getValue(), CheckRuleUniqueKey.DDL_CHECK_CREATE_TIME_COLUMN.getName(), "create_time"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_UPDATE_TIME_COLUMN.getValue(), CheckRuleUniqueKey.DDL_CHECK_UPDATE_TIME_COLUMN.getName(), "update_time"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_TABLE_MUST_TABLESPACE.getValue(), CheckRuleUniqueKey.DDL_CHECK_TABLE_MUST_TABLESPACE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_CHECK_FORBIDDEN_TABLESPACE.getValue(), CheckRuleUniqueKey.DDL_CHECK_FORBIDDEN_TABLESPACE.getName(), "system,sys"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DDL_DISABLE_TYPE_TIMESTAMP.getValue(), CheckRuleUniqueKey.DDL_DISABLE_TYPE_TIMESTAMP.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_SQL_LENGTH.getValue(), CheckRuleUniqueKey.DML_CHECK_SQL_LENGTH.getName(), "58"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_DISABLE_SELECT_ALL_COLUMN.getValue(), CheckRuleUniqueKey.DML_DISABLE_SELECT_ALL_COLUMN.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.ALL_CHECK_WHERE_IS_INVALID.getValue(), CheckRuleUniqueKey.ALL_CHECK_WHERE_IS_INVALID.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.ALL_CHECK_SYNTAX_ERROR.getValue(), CheckRuleUniqueKey.ALL_CHECK_SYNTAX_ERROR.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_ALIAS.getValue(), CheckRuleUniqueKey.DML_CHECK_ALIAS.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_FUZZY_SEARCH.getValue(), CheckRuleUniqueKey.DML_CHECK_FUZZY_SEARCH.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_WHERE_EXIST_NOT.getValue(), CheckRuleUniqueKey.DML_CHECK_WHERE_EXIST_NOT.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_WHERE_EXIST_SCALAR_SUB_QUERIES.getValue(), CheckRuleUniqueKey.DML_CHECK_WHERE_EXIST_SCALAR_SUB_QUERIES.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_NOT_RECOMMEND_SUBQUERY.getValue(), CheckRuleUniqueKey.DML_NOT_RECOMMEND_SUBQUERY.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_NOT_RECOMMEND_EXPRESSION_IN_WHERE.getValue(), CheckRuleUniqueKey.DML_NOT_RECOMMEND_EXPRESSION_IN_WHERE.getName(), ""));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_IN_QUERY_LIMIT.getValue(), CheckRuleUniqueKey.DML_CHECK_IN_QUERY_LIMIT.getName(), "2"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_SUB_QUERY_DEPTH.getValue(), CheckRuleUniqueKey.DML_CHECK_SUB_QUERY_DEPTH.getName(), "3"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.DML_CHECK_AFFECTED_ROWS.getValue(), CheckRuleUniqueKey.DML_CHECK_AFFECTED_ROWS.getName(), "3"));
        sqlCheckRuleParamList.add(buildCheckRuleContent(CheckRuleDatabasePrefix.ORACLE_.getValue() + CheckRuleUniqueKey.ALL_CHECK_OPERATION_HIGH_RISK.getValue(), CheckRuleUniqueKey.ALL_CHECK_OPERATION_HIGH_RISK.getName(), "delete,select"));
        return sqlCheckRuleParamList;
    }

    public CheckRuleContent buildCheckRuleContent(String uniqueKey, String name, String value) {
        Map<String, String> paramsMap = new LinkedHashMap<>();
        paramsMap.put("first_key", value);
        CheckRuleContent checkRuleContent = new CheckRuleContent();
        checkRuleContent.setUniqueKey(uniqueKey);
        checkRuleContent.setRuleName(name);
        checkRuleContent.setParamsMap(paramsMap);
        return checkRuleContent;
    }

    public CheckRuleContent buildCheckRuleContent(String uniqueKey, String name, String value1, String value2) {
        Map<String, String> paramsMap = new LinkedHashMap<>();
        paramsMap.put("first_key", value1);
        paramsMap.put("second_key", value2);
        CheckRuleContent checkRuleContent = new CheckRuleContent();
        checkRuleContent.setUniqueKey(uniqueKey);
        checkRuleContent.setRuleName(name);
        checkRuleContent.setParamsMap(paramsMap);
        return checkRuleContent;
    }

    public List<CheckResult> checkSyntaxError(List<CheckRuleContent> sqlCheckRuleParamList, Integer dbType) {
        for (CheckRuleContent checkRuleContent : sqlCheckRuleParamList) {
            if (checkRuleContent.getUniqueKey().equals(CheckRuleDatabasePrefix.of(dbType).getValue() + CheckRuleUniqueKey.ALL_CHECK_SYNTAX_ERROR.getValue())) {
                return List.of(CheckResult.buildFailResult(checkRuleContent));
            }
        }
        return null;
    }

}
