package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.dml.LoadDataStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaLoadDataStatement extends LoadDataStatement implements ImpalaStatement {

    private PartitionPropertiesSegment partitionProperties;

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.ofNullable(partitionProperties);
    }
}
