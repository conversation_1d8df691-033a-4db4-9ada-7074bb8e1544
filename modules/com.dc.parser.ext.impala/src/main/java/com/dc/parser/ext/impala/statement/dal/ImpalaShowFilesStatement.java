package com.dc.parser.ext.impala.statement.dal;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dal.ShowFilesStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public class ImpalaShowFilesStatement extends ShowFilesStatement implements ImpalaStatement {

    private SimpleTableSegment inTable;

    private PartitionPropertiesSegment partitionProperties;

    public Optional<SimpleTableSegment> getInTable() {
        return Optional.ofNullable(inTable);
    }

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.ofNullable(partitionProperties);
    }

}
