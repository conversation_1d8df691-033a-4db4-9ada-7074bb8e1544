package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.HintSegment;
import com.dc.parser.model.segment.database.impala.SelectIntoSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.dml.predicate.LockSegment;
import com.dc.parser.model.segment.generic.WindowSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaSelectStatement extends SelectStatement implements ImpalaStatement {

    private SimpleTableSegment table;

    private LimitSegment limit;

    private LockSegment lock;

    private WindowSegment window;

    private HintSegment hint;

    private SelectIntoSegment selectIntoSegment;

    public Optional<LimitSegment> getLimit() {
        return Optional.ofNullable(limit);
    }

    public Optional<LockSegment> getLock() {
        return Optional.ofNullable(lock);
    }

    public Optional<WindowSegment> getWindow() {
        return Optional.ofNullable(window);
    }

    /**
     * Get simple table segment.
     *
     * @return simple table segment
     */
    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }

    public Optional<HintSegment> getHint() {
        return Optional.ofNullable(hint);
    }

    public Optional<SelectIntoSegment> getSelectIntoSegment() {
        return Optional.ofNullable(selectIntoSegment);
    }



}
