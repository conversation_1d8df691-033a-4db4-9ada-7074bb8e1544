package com.dc.parser.ext.impala.visitor.statement.type;

import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementParser.*;
import com.dc.parser.model.segment.database.impala.GrantOnSegment;
import com.dc.parser.model.segment.database.impala.GrantToSegment;
import com.dc.parser.model.segment.database.impala.RevokeOnSegment;
import com.dc.parser.model.segment.database.impala.RevokeToSegment;
import com.dc.parser.ext.impala.statement.dcl.ImpalaGrantStatement;
import com.dc.parser.ext.impala.statement.dcl.ImpalaRevokeStatement;
import com.dc.parser.ext.impala.statement.ddl.ImpalaDropRoleStatement;
import com.dc.parser.ext.impala.visitor.statement.ImpalaStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DCLStatementVisitor;
import com.dc.parser.model.segment.dcl.PrivilegeSegment;
import com.dc.parser.model.segment.dcl.RoleOrPrivilegeSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Collection;
import java.util.LinkedList;

public final class ImpalaDCLStatementVisitor extends ImpalaStatementVisitor implements DCLStatementVisitor {

    @Override
    public ASTNode visitGrant(GrantContext ctx) {
        ImpalaGrantStatement result = new ImpalaGrantStatement();
        if (ctx.grantRoleToGroup() != null) {
            GrantRoleToGroupContext ctx2 = ctx.grantRoleToGroup();
            result.getRoleOrPrivilege().add(new RoleOrPrivilegeSegment(ctx2.GRANT().getSymbol().getStartIndex(), ctx2.roleName().stop.getStopIndex(),
                    getOriginalText(ctx2.roleName()), null, null));
            GrantToSegment grantToSegment = new GrantToSegment();
            grantToSegment.setGroup(getOriginalText(ctx2.groupName()));
            result.setGrantTo(grantToSegment);
            return result;
        }
        if (ctx.grantPrivilegeOnObjectToPrincipal() != null) {
            GrantPrivilegeOnObjectToPrincipalContext ctx2 = ctx.grantPrivilegeOnObjectToPrincipal();
            PrivilegeSegment privilegeSegment = new PrivilegeSegment(ctx2.privilege().start.getStartIndex(), ctx2.privilege().stop.getStopIndex(), getOriginalText(ctx2.privilege()));
            if (ctx2.privilege().SELECT() != null && ctx2.privilege().columnNames() != null) {
                ColumnNamesContext columnNamesContext = ctx2.privilege().columnNames();
                Collection<String> strs = new LinkedList<>();
                for (ColumnNameContext each : columnNamesContext.columnName()) {
                    strs.add(getOriginalText(each));
                }
                privilegeSegment = new PrivilegeSegment(ctx2.privilege().start.getStartIndex(), ctx2.privilege().stop.getStopIndex(), ctx2.privilege().SELECT().getText());
                privilegeSegment.getColumns().addAll(strs);
            }
            result.getRoleOrPrivilege().add(new RoleOrPrivilegeSegment(ctx2.privilege().start.getStartIndex(), ctx2.privilege().stop.getStopIndex(),
                    null, null, privilegeSegment));
            GrantObjectContext grantObjectContext = ctx2.grantObject();
            GrantOnSegment grantOnSegment = new GrantOnSegment();
            if (grantObjectContext.SERVER() != null && grantObjectContext.name() != null) {
                grantOnSegment.setServer(getOriginalText(grantObjectContext.name()));
            } else if (grantObjectContext.URI() != null) {
                grantOnSegment.setUri(grantObjectContext.STRING_().getText());
            } else if (grantObjectContext.DATABASE() != null) {
                grantOnSegment.setDatabase((DatabaseSegment) visitDatabaseName(grantObjectContext.databaseName()));
            } else if (grantObjectContext.TABLE() != null) {
                grantOnSegment.setTable((SimpleTableSegment) visitTableName(grantObjectContext.tableName()));
            }
            result.setGrantOn(grantOnSegment);

            GrantToSuffixContext grantToSuffixContext = ctx2.grantToSuffix();
            GrantToSegment grantToSegment = new GrantToSegment();
            if (grantToSuffixContext.USER() != null) {
                grantToSegment.setUser(getOriginalText(grantToSuffixContext.username()));
            } else if (grantToSuffixContext.GROUP() != null) {
                grantToSegment.setGroup(getOriginalText(grantToSuffixContext.groupName()));
            } else if (grantToSuffixContext.ROLE() != null) {
                grantToSegment.setRole(getOriginalText(grantToSuffixContext.roleName()));
            }
            result.setGrantTo(grantToSegment);
            result.setWithGrantOption(ctx2.withGrantOption() != null);
            return result;
        }
        return result;
    }

    @Override
    public ASTNode visitRevoke(RevokeContext ctx) {
        ImpalaRevokeStatement result = new ImpalaRevokeStatement();
        if (ctx.revokeRoleFromGroup() != null) {
            RevokeRoleFromGroupContext child1 = ctx.revokeRoleFromGroup();
            result.getRoleOrPrivilege().add(new RoleOrPrivilegeSegment(child1.ROLE().getSymbol().getStartIndex(), child1.roleName().stop.getStopIndex(),
                    getOriginalText(child1.roleName()), null, null));
            RevokeToSegment revokeToSegment = new RevokeToSegment();
            revokeToSegment.setGroup(getOriginalText(child1.groupName()));
            result.setRevokeTo(revokeToSegment);
            return result;
        }
        if (ctx.revokePrivilegeOnObjectFromPrincipal() != null) {
            RevokePrivilegeOnObjectFromPrincipalContext child2 = ctx.revokePrivilegeOnObjectFromPrincipal();
            result.setGrantOptionFor(child2.OPTION() != null);
            PrivilegeSegment privilegeSegment = new PrivilegeSegment(child2.privilege().start.getStartIndex(), child2.privilege().stop.getStopIndex(), getOriginalText(child2.privilege()));
            if (child2.privilege().SELECT() != null && child2.privilege().columnNames() != null) {
                ColumnNamesContext columnNamesContext = child2.privilege().columnNames();
                Collection<String> strs = new LinkedList<>();
                for (ColumnNameContext each : columnNamesContext.columnName()) {
                    strs.add(getOriginalText(each));
                }
                privilegeSegment = new PrivilegeSegment(child2.privilege().start.getStartIndex(), child2.privilege().stop.getStopIndex(), child2.privilege().SELECT().getText());
                privilegeSegment.getColumns().addAll(strs);
            }
            result.getRoleOrPrivilege().add(new RoleOrPrivilegeSegment(child2.privilege().start.getStartIndex(), child2.privilege().stop.getStopIndex(),
                    null, null, privilegeSegment));
            GrantObjectContext grantObjectContext = child2.grantObject();
            RevokeOnSegment revokeOnSegment = new RevokeOnSegment();
            if (grantObjectContext.SERVER() != null && grantObjectContext.name() != null) {
                revokeOnSegment.setServer(getOriginalText(grantObjectContext.name()));
            } else if (grantObjectContext.URI() != null) {
                revokeOnSegment.setUri(grantObjectContext.STRING_().getText());
            } else if (grantObjectContext.DATABASE() != null) {
                revokeOnSegment.setDatabase((DatabaseSegment) visitDatabaseName(grantObjectContext.databaseName()));
            } else if (grantObjectContext.TABLE() != null) {
                revokeOnSegment.setTable((SimpleTableSegment) visitTableName(grantObjectContext.tableName()));
            }
            result.setRevokeOn(revokeOnSegment);

            RevokeToSegment revokeToSegment = new RevokeToSegment();
            if (child2.revokeFromSuffix() != null) {
                if (child2.revokeFromSuffix().USER() != null) {
                    revokeToSegment.setUser(getOriginalText(child2.revokeFromSuffix().username()));
                } else if (child2.revokeFromSuffix().GROUP() != null) {
                    revokeToSegment.setGroup(getOriginalText(child2.revokeFromSuffix().groupName()));
                }
            }
            if (child2.revokeFromRoleSuffix() != null) {
                revokeToSegment.setRole(getOriginalText(child2.revokeFromRoleSuffix().roleName()));
            }
            result.setRevokeTo(revokeToSegment);

            return result;
        }

        return result;
    }

    @Override
    public ASTNode visitDropRole(DropRoleContext ctx) {
        ImpalaDropRoleStatement result = new ImpalaDropRoleStatement();
        result.setRoleName(new IdentifierValue(getOriginalText(ctx.name())));
        return result;
    }
}
