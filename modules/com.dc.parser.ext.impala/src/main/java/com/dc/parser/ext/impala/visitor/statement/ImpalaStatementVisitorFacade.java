package com.dc.parser.ext.impala.visitor.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.ext.impala.visitor.statement.type.ImpalaDALStatementVisitor;
import com.dc.parser.ext.impala.visitor.statement.type.ImpalaDCLStatementVisitor;
import com.dc.parser.ext.impala.visitor.statement.type.ImpalaDDLStatementVisitor;
import com.dc.parser.ext.impala.visitor.statement.type.ImpalaDMLStatementVisitor;
import com.dc.parser.model.api.visitor.statement.type.*;
import com.dc.parser.model.spi.SQLStatementVisitorFacade;

public class ImpalaStatementVisitorFacade implements SQLStatementVisitorFacade {

    @Override
    public Class<? extends DMLStatementVisitor> getDMLVisitorClass() {
        return ImpalaDMLStatementVisitor.class;
    }

    @Override
    public Class<? extends DDLStatementVisitor> getDDLVisitorClass() {
        return ImpalaDDLStatementVisitor.class;
    }

    @Override
    public Class<? extends TCLStatementVisitor> getTCLVisitorClass() {
        //don't have
        throw new UnsupportedOperationException("");
    }

    @Override
    public Class<? extends DCLStatementVisitor> getDCLVisitorClass() {
        return ImpalaDCLStatementVisitor.class;
    }

    @Override
    public Class<? extends DALStatementVisitor> getDALVisitorClass() {
        return ImpalaDALStatementVisitor.class;
    }

    @Override
    public Class<? extends RLStatementVisitor> getRLVisitorClass() {
        //don't have
        throw new UnsupportedOperationException("");
    }

    @Override
    public Class<? extends BatchStatementVisitor> getBatchVisitorClass() {
        return null;
    }

    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.IMPALA;
    }
}
