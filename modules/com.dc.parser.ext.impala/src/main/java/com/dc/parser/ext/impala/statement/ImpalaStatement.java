package com.dc.parser.ext.impala.statement;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.model.statement.SQLStatement;

/**
 * Impala Statement
 */
public interface ImpalaStatement extends SQLStatement {

    @Override
    default DatabaseType getDatabaseType() {
        return TypedSPILoader.getService(DatabaseType.class, DatabaseType.Constant.IMPALA);
    }
}
