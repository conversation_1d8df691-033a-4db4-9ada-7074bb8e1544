package com.dc.parser.ext.impala.parser;

import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementParser;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.model.engine.ParseASTNode;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.TokenStream;

public final class ImpalaParser extends ImpalaStatementParser implements SQLParser {

    public ImpalaParser(TokenStream input) {
        super(input);
    }

    @Override
    public ASTNode parse() {
        return new ParseASTNode(root(), (CommonTokenStream) getTokenStream());
    }
}
