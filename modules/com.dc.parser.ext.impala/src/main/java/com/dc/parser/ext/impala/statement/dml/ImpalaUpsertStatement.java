package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.HintSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.dml.assignment.InsertValuesSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.statement.dml.UpsertStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Setter
public final class ImpalaUpsertStatement extends UpsertStatement implements ImpalaStatement {

    @Getter
    private final List<HintSegment> hints = new LinkedList<>();

    private SubquerySegment select;

    @Getter
    private final Collection<InsertValuesSegment> values = new LinkedList<>();

    public Optional<SubquerySegment> getSelect() {
        return Optional.ofNullable(select);
    }

}
