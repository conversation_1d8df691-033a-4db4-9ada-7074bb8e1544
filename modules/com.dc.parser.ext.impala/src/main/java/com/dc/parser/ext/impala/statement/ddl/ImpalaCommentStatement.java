package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.statement.ddl.CommentStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ImpalaCommentStatement extends CommentStatement implements ImpalaStatement {

    private DatabaseSegment database;
}
