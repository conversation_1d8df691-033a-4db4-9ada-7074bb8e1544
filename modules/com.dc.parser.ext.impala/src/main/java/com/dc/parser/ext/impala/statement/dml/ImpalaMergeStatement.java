package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.HintSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.dml.merge.MergeWhenAndThenSegment;
import com.dc.parser.model.statement.dml.MergeStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

@Setter
@Getter
public final class ImpalaMergeStatement extends MergeStatement implements ImpalaStatement {

    private List<HintSegment> hints = new LinkedList<>();

    private Collection<MergeWhenAndThenSegment> whenAndThenSegments = new LinkedList<>();
}
