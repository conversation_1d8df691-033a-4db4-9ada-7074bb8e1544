package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.InvalidateMetadataStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaInvalidateMetadataStatement extends InvalidateMetadataStatement implements ImpalaStatement {

    private SimpleTableSegment table;

    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }

}
