package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.SelectFromValuesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.dml.ValuesStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaValuesStatement extends ValuesStatement implements ImpalaStatement {

    private SelectFromValuesSegment selectFromValuesSegment;

    public Optional<SelectFromValuesSegment> getSelectFromValuesSegment() {
        return Optional.ofNullable(selectFromValuesSegment);
    }

}
