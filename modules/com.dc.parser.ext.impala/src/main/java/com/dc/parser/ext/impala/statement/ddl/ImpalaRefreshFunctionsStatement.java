package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.ddl.RefreshFunctionsStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaRefreshFunctionsStatement extends RefreshFunctionsStatement implements ImpalaStatement {

    private IdentifierValue dbName;

    public Optional<IdentifierValue> getDbName() {
        return Optional.ofNullable(dbName);
    }

}
