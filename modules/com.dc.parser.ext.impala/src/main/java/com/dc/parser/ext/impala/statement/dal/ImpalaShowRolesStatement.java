package com.dc.parser.ext.impala.statement.dal;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.dal.ShowRolesStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaShowRolesStatement extends ShowRolesStatement implements ImpalaStatement {

    private String groupName;

    public Optional<String> getGroupName() {
        return Optional.ofNullable(groupName);
    }

}
