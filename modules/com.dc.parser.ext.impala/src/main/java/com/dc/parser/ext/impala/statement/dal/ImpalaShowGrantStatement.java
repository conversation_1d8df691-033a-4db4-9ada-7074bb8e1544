package com.dc.parser.ext.impala.statement.dal;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.dcl.UserSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dal.ShowGrantStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public final class ImpalaShowGrantStatement extends ShowGrantStatement implements ImpalaStatement {

    private ShowGrantUserSegment showGrantUserSegment;
    private ShowGrantRoleSegment showGrantRoleSegment;
    private ShowGrantGroupSegment showGrantGroupSegment;

    @Setter
    @Getter
    public static class ShowGrantUserSegment implements SQLSegment {
        private int startIndex;
        private int stopIndex;
        private UserSegment user;
        private ShowOnSegment showOnSegment;
    }
    @Setter
    @Getter
    public static class ShowGrantRoleSegment implements SQLSegment {
        private int startIndex;
        private int stopIndex;
        private String role;
        private ShowOnSegment showOnSegment;
    }
    @Setter
    @Getter
    public static class ShowGrantGroupSegment implements SQLSegment {
        private int startIndex;
        private int stopIndex;
        private String group;
        private ShowOnSegment showOnSegment;
    }
    @Setter
    @Getter
    public static class ShowOnSegment implements SQLSegment {
        private int startIndex;
        private int stopIndex;
        private String onServer;
        private DatabaseSegment onDatabase;
        private SimpleTableSegment onTable;
        private String onUri;
        private ColumnSegment onColumn;
    }
}
