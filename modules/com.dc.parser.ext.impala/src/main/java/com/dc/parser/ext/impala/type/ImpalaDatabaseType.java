package com.dc.parser.ext.impala.type;

import com.dc.infra.database.type.DatabaseType;

import java.util.Collection;
import java.util.Collections;

public final class ImpalaDatabaseType implements DatabaseType {

    @Override
    public Collection<String> getJdbcUrlPrefixes() {
        return Collections.singletonList("jdbc:hive:");
    }

    @Override
    public Constant getType() {
        return Constant.IMPALA;
    }
}
