package com.dc.parser.ext.impala.visitor.statement.type;

import com.dc.parser.model.enums.HintSegmentType;
import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementParser.*;
import com.dc.parser.model.segment.database.impala.HintSegment;
import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.model.segment.database.impala.SelectFromValuesSegment;
import com.dc.parser.model.segment.database.impala.SelectIntoSegment;
import com.dc.parser.ext.impala.statement.dml.*;
import com.dc.parser.ext.impala.visitor.statement.ImpalaStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DMLStatementVisitor;
import com.dc.parser.model.enums.AggregationType;
import com.dc.parser.model.enums.CombineType;
import com.dc.parser.model.enums.JoinType;
import com.dc.parser.model.enums.OrderDirection;
import com.dc.parser.model.segment.dal.VariableSegment;
import com.dc.parser.model.segment.dml.assignment.ColumnAssignmentSegment;
import com.dc.parser.model.segment.dml.assignment.InsertValuesSegment;
import com.dc.parser.model.segment.dml.assignment.SetAssignmentSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.column.InsertColumnsSegment;
import com.dc.parser.model.segment.dml.combine.CombineSegment;
import com.dc.parser.model.segment.dml.expr.*;
import com.dc.parser.model.segment.dml.expr.complex.CommonExpressionSegment;
import com.dc.parser.model.segment.dml.expr.complex.CommonTableExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.ParameterMarkerExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.item.*;
import com.dc.parser.model.segment.dml.merge.MergeWhenAndThenSegment;
import com.dc.parser.model.segment.dml.order.GroupBySegment;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.order.item.ColumnOrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.ExpressionOrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.IndexOrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.OrderByItemSegment;
import com.dc.parser.model.segment.dml.pagination.PaginationValueSegment;
import com.dc.parser.model.segment.dml.pagination.limit.LimitSegment;
import com.dc.parser.model.segment.dml.pagination.limit.NumberLiteralLimitValueSegment;
import com.dc.parser.model.segment.dml.pagination.limit.ParameterMarkerLimitValueSegment;
import com.dc.parser.model.segment.dml.predicate.HavingSegment;
import com.dc.parser.model.segment.dml.predicate.LockSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.*;
import com.dc.parser.model.segment.generic.table.*;
import com.dc.parser.model.util.SQLUtils;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.model.value.literal.impl.BooleanLiteralValue;
import com.dc.parser.model.value.literal.impl.NumberLiteralValue;
import com.dc.parser.model.value.literal.impl.OtherLiteralValue;
import com.dc.parser.model.value.literal.impl.StringLiteralValue;
import com.dc.parser.model.value.parametermarker.ParameterMarkerValue;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.misc.Interval;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class ImpalaDMLStatementVisitor extends ImpalaStatementVisitor implements DMLStatementVisitor {

    @Override
    public ASTNode visitSelect(SelectContext ctx) {
        ImpalaSelectStatement result;
        //queryExpression
        if (ctx.queryExpression() != null) {
            result = (ImpalaSelectStatement) visitQueryExpression(ctx.queryExpression());
            if (ctx.lockClauseList() != null) {
                result.setLock((LockSegment) visitLockClauseList(ctx.lockClauseList()));
            }
        } else if (ctx.selectWithInto() != null) {
            result = (ImpalaSelectStatement) visitSelectWithInto(ctx.selectWithInto());
        } else {
            result = (ImpalaSelectStatement) visit(ctx.getChild(0));
        }

        result.addParameterMarkerSegments(getParameterMarkerSegments());

        return result;
    }

    private boolean isDistinct(final QuerySpecificationContext ctx) {
        for (SelectSpecificationContext each : ctx.selectSpecification()) {
            if (((BooleanLiteralValue) visitSelectSpecification(each)).getValue()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public ASTNode visitSelectSpecification(final SelectSpecificationContext ctx) {
        if (null != ctx.duplicateSpecification()) {
            return visitDuplicateSpecification(ctx.duplicateSpecification());
        }
        return new BooleanLiteralValue(false);
    }

    @Override
    public ASTNode visitDuplicateSpecification(final DuplicateSpecificationContext ctx) {
        String text = getOriginalText(ctx);
        if ("DISTINCT".equalsIgnoreCase(text) || "DISTINCTROW".equalsIgnoreCase(text)) {
            return new BooleanLiteralValue(true);
        }
        return new BooleanLiteralValue(false);
    }

    @Override
    public ASTNode visitSelectWithInto(final SelectWithIntoContext ctx) {
        if (null != ctx.selectWithInto()) {
            return visitSelectWithInto(ctx.selectWithInto());
        }
        ImpalaSelectStatement result = (ImpalaSelectStatement) visitQueryExpression(ctx.queryExpression());
        if (null != ctx.lockClauseList()) {
            result.setLock((LockSegment) visitLockClauseList(ctx.lockClauseList()));
        }
        return result;
    }

    @Override
    public ASTNode visitQueryExpression(QueryExpressionContext ctx) {
        ImpalaSelectStatement result;

        //queryExpressionBody
        if (ctx.queryExpressionBody() != null) {
            result = (ImpalaSelectStatement) visitQueryExpressionBody(ctx.queryExpressionBody());
        } else {
            //queryExpressionParens
            result = (ImpalaSelectStatement) visitQueryExpressionParens(ctx.queryExpressionParens());
        }

        //orderByClause
        if (null != ctx.orderByClause()) {
            result.setOrderBy((OrderBySegment) visitOrderByClause(ctx.orderByClause()));
        }
        //limitClause
        if (null != ctx.limitClause()) {
            result.setLimit((LimitSegment) visitLimitClause(ctx.limitClause()));
        }
        //withClause
        if (ctx.withClause() != null) {
            result.setWithSegment((WithSegment) visitWithClause(ctx.withClause()));
        }

        return result;
    }

    @Override
    public ASTNode visitWithClause(WithClauseContext ctx) {
        Collection<CommonTableExpressionSegment> commonTableExpressions = new LinkedList<>();
        if (ctx.cteClause() != null) {
            for (CteClauseContext each : ctx.cteClause()) {
                Index queryIndex = getQueryIndexInParens(each.subquery().queryExpressionParens());
                SubquerySegment subquery = new SubquerySegment(queryIndex.startIndex, queryIndex.stopIndex,
                                                                (ImpalaSelectStatement) visitSubquery(each.subquery()), getOriginalText(each));
                CommonTableExpressionSegment commonTableExpression = new CommonTableExpressionSegment(each.start.getStartIndex(), each.stop.getStopIndex(), null, subquery);
                commonTableExpression.setAlias(new AliasSegment(each.identifier().start.getStartIndex(), each.identifier().stop.getStopIndex(), new IdentifierValue(each.identifier().getText())));
                if (each.columnNames() != null) {
                    each.columnNames().columnName().forEach(columnNameContext -> {
                        commonTableExpression.getColumns().add((ColumnSegment) visitColumnName(columnNameContext));
                    });
                }
                commonTableExpressions.add(commonTableExpression);

            }
        }

        boolean recursive = ctx.RECURSIVE() != null;
        return new WithSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), commonTableExpressions, recursive);
    }


    private Index getQueryIndexInParens(QueryExpressionParensContext queryExpressionParens) {
        QueryExpressionContext queryExpression = null;
        //只有一层括号
        if (queryExpressionParens.queryExpressionParens() == null) {
            queryExpression = queryExpressionParens.queryExpression();
            return new Index(queryExpression.start.getStartIndex(), queryExpression.stop.getStopIndex());
        }
        QueryExpressionParensContext parens = queryExpressionParens.queryExpressionParens();
        while (parens.queryExpressionParens() != null) {
            parens = parens.queryExpressionParens();
        }
        //这里说明没有括号了，子元素一定是queryExpression
        QueryExpressionContext query = parens.queryExpression();
        return new Index(query.start.getStartIndex(), query.stop.getStopIndex());
    }

    public static class Index {
        int startIndex;
        int stopIndex;
        Index(int startIndex, int stopIndex) {
            this.startIndex = startIndex;
            this.stopIndex = stopIndex;
        }
    }

    @Override
    public ASTNode visitQueryExpressionBody(QueryExpressionBodyContext ctx) {
        //queryPrimary
        if (ctx.getChildCount() == 1 && ctx.getChild(0) instanceof QueryPrimaryContext) {
            return visitQueryPrimary(ctx.queryPrimary());
        }

        //queryExpressionParens
        if (null != ctx.queryExpressionParens()) {
            ImpalaSelectStatement result = new ImpalaSelectStatement();
            SubquerySegment left = new SubquerySegment(ctx.queryExpressionParens().start.getStartIndex(), ctx.queryExpressionParens().stop.getStopIndex(),
                    (ImpalaSelectStatement) visitQueryExpressionParens(ctx.queryExpressionParens()), getOriginalText(ctx.queryExpressionParens()));
            result.setProjections(left.getSelect().getProjections());
            left.getSelect().getFrom().ifPresent(result::setFrom);
            ((ImpalaSelectStatement) left.getSelect()).getTable().ifPresent(result::setTable);
            result.setCombine(createCombineSegment(ctx.combineClause(), left));
            return result;
        }
        //queryExpressionBody
        if (null != ctx.queryExpressionBody()) {
            ImpalaSelectStatement result = new ImpalaSelectStatement();
            SubquerySegment left = new SubquerySegment(ctx.queryExpressionBody().start.getStartIndex(), ctx.queryExpressionBody().stop.getStopIndex(),
                    (ImpalaSelectStatement) visitQueryExpressionBody(ctx.queryExpressionBody()), getOriginalText(ctx.queryExpressionBody()));
            result.setProjections(left.getSelect().getProjections());
            left.getSelect().getFrom().ifPresent(result::setFrom);
            ((ImpalaSelectStatement) left.getSelect()).getTable().ifPresent(result::setTable);
            result.setCombine(createCombineSegment(ctx.combineClause(), left));
            return result;
        }

        return visit(ctx.queryExpressionParens());
    }

    private CombineSegment createCombineSegment(final CombineClauseContext ctx, final SubquerySegment left) {
        CombineType combineType;
        if (null != ctx.EXCEPT()) {
            combineType = CombineType.EXCEPT;
        } else {
            combineType = null == ctx.combineOption() || null == ctx.combineOption().ALL() ? CombineType.UNION : CombineType.UNION_ALL;
        }
        ParserRuleContext ruleContext = null == ctx.queryPrimary() ? ctx.queryExpressionParens() : ctx.queryPrimary();
        SubquerySegment right = new SubquerySegment(ruleContext.start.getStartIndex(), ruleContext.stop.getStopIndex(), (ImpalaSelectStatement) visit(ruleContext), getOriginalText(ruleContext));
        return new CombineSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), left, combineType, right);
    }

    @Override
    public ASTNode visitQueryExpressionParens(QueryExpressionParensContext ctx) {
        if (null != ctx.queryExpressionParens()) {
            return visitQueryExpressionParens(ctx.queryExpressionParens());
        }
        ImpalaSelectStatement result = (ImpalaSelectStatement) visitQueryExpression(ctx.queryExpression());
        if (null != ctx.lockClauseList()) {
            result.setLock((LockSegment) visitLockClauseList(ctx.lockClauseList()));
        }
        result.addParameterMarkerSegments(getParameterMarkerSegments());
        return result;
    }

    @Override
    public ASTNode visitLockClauseList(final LockClauseListContext ctx) {
        LockSegment result = new LockSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        for (LockClauseContext each : ctx.lockClause()) {
            if (null != each.tableLockingList()) {
                result.getTables().addAll(generateTablesFromTableAliasRefList(each.tableLockingList().tableAliasRefList()));
            }
        }
        return result;
    }

    private List<SimpleTableSegment> generateTablesFromTableAliasRefList(final TableAliasRefListContext ctx) {
        List<SimpleTableSegment> result = new LinkedList<>();
        for (TableIdentOptWildContext each : ctx.tableIdentOptWild()) {
            result.add((SimpleTableSegment) visitTableName(each.tableName()));
        }
        return result;
    }

    @Override
    public ASTNode visitQueryPrimary(QueryPrimaryContext ctx) {
        if (ctx.querySpecification() != null) {
            return visitQuerySpecification(ctx.querySpecification());
        } else if (ctx.tableValueConstructor() != null) {
            return visitTableValueConstructor(ctx.tableValueConstructor());
        } else {
            return visitTableStatement(ctx.tableStatement());
        }
    }

    @Override
    public ASTNode visitTableValueConstructor(final TableValueConstructorContext ctx) {
        ImpalaSelectStatement result = new ImpalaSelectStatement();
        int startIndex = ctx.getStart().getStartIndex();
        int stopIndex = ctx.getStop().getStopIndex();
        ValuesExpression valuesExpression = new ValuesExpression(startIndex, stopIndex);
        valuesExpression.getRowConstructorList().addAll(createRowConstructorList(ctx.rowConstructorList()));
        result.setProjections(new ProjectionsSegment(startIndex, stopIndex));
        result.getProjections().getProjections().add(new ExpressionProjectionSegment(startIndex, stopIndex, getOriginalText(ctx), valuesExpression));
        return result;
    }

    private Collection<InsertValuesSegment> createRowConstructorList(final RowConstructorListContext ctx) {
        Collection<InsertValuesSegment> result = new LinkedList<>();
        for (AssignmentValuesContext each : ctx.assignmentValues()) {
            result.add((InsertValuesSegment) visitAssignmentValues(each));
        }
        return result;
    }

    @Override
    public ASTNode visitTableStatement(final TableStatementContext ctx) {
        ImpalaSelectStatement result = new ImpalaSelectStatement();
        if (null != ctx.TABLE()) {
            result.setFrom(new SimpleTableSegment(new TableNameSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(),
                    new IdentifierValue(ctx.tableName().getText()))));
        } else {
            result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        }
        return result;
    }

    @Override
    public ASTNode visitAssignmentValues(final AssignmentValuesContext ctx) {
        List<ExpressionSegment> segments = new LinkedList<>();
        for (AssignmentValueContext each : ctx.assignmentValue()) {
            segments.add((ExpressionSegment) visitAssignmentValue(each));
        }
        return new InsertValuesSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), segments);
    }

    @Override
    public ASTNode visitAssignmentValue(final AssignmentValueContext ctx) {
        ExprContext expr = ctx.expr();
        if (null != expr) {
            ASTNode result = visitExpr(expr);
            if (result instanceof ColumnSegment) {
                return new CommonExpressionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getOriginalText(ctx));
            } else {
                return result;
            }
        }
        return new CommonExpressionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getOriginalText(ctx));
    }

    @Override
    public ASTNode visitHint(HintContext ctx) {
        HintSegment result = new HintSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx));
        if (null != ctx.BLOCK_HINT()) {
            result.setHintSegmentType(HintSegmentType.BLOCK_HINT);
        } else if (null != ctx.INLINE_HINT()) {
            result.setHintSegmentType(HintSegmentType.INLINE_HINT);
        }
        return result;
    }

    @Override
    public ASTNode visitSelectIntoExpression(SelectIntoExpressionContext ctx) {
        SelectIntoSegment result = new SelectIntoSegment();
        if (null != ctx.variable() && !ctx.variable().isEmpty()) {
            Collection<TableSegment> intos = new ArrayList<>();
            for (VariableContext variable : ctx.variable()) {
                intos.add(getOneIntoByVariable(variable));
            }
            result.setIntos(intos);
        }
        result.setStartIndex(ctx.start.getStartIndex());
        result.setStopIndex(ctx.stop.getStopIndex());
        return result;
    }

    public TableSegment getOneIntoByVariable(VariableContext ctx) {
        if (ctx.userVariable() != null) {
            UserVariableContext userVariable = ctx.userVariable();
            TextOrIdentifierContext textOrIdentifier = userVariable.textOrIdentifier();
            if (userVariable.AT_() != null) {
                SimpleTableSegment simpleTableSegment = new SimpleTableSegment(new TableNameSegment(textOrIdentifier.start.getStartIndex(), textOrIdentifier.stop.getStopIndex(), new IdentifierValue(textOrIdentifier.getText())));
                simpleTableSegment.setAt(new IdentifierValue(userVariable.AT_().getText()));
                return simpleTableSegment;
            } else {
                return new SimpleTableSegment(new TableNameSegment(textOrIdentifier.start.getStartIndex(), textOrIdentifier.stop.getStopIndex(), new IdentifierValue(textOrIdentifier.getText())));
            }
        } else {
            SystemVariableContext systemVariable = ctx.systemVariable();
            RvalueSystemVariableContext rvalueSystemVariable = systemVariable.rvalueSystemVariable();
            if (rvalueSystemVariable.DOT_() != null) {
                TextOrIdentifierContext textOrIdentifier = rvalueSystemVariable.textOrIdentifier();
                IdentifierContext identifier = rvalueSystemVariable.identifier();
                SimpleTableSegment simpleTableSegment = new SimpleTableSegment(new TableNameSegment(identifier.start.getStartIndex(), identifier.stop.getStopIndex(), new IdentifierValue(identifier.getText())));
                simpleTableSegment.setOwner(new OwnerSegment(textOrIdentifier.start.getStartIndex(), textOrIdentifier.stop.getStopIndex(), new IdentifierValue(textOrIdentifier.getText())));
                simpleTableSegment.setAt(new IdentifierValue(systemVariable.AT_(0).getText() + systemVariable.AT_(1)));
                return simpleTableSegment;
            } else {
                TextOrIdentifierContext textOrIdentifier = rvalueSystemVariable.textOrIdentifier();
                SimpleTableSegment simpleTableSegment = new SimpleTableSegment(new TableNameSegment(textOrIdentifier.start.getStartIndex(), textOrIdentifier.stop.getStopIndex(), new IdentifierValue(textOrIdentifier.getText())));
                simpleTableSegment.setAt(new IdentifierValue(systemVariable.AT_(0).getText() + systemVariable.AT_(1)));
                return simpleTableSegment;
            }
        }
    }

    @Override
    public ASTNode visitQuerySpecification(QuerySpecificationContext ctx) {
        ImpalaSelectStatement result = new ImpalaSelectStatement();
        //hint
        if (null != ctx.hint()) {
            result.setHint((HintSegment) visitHint(ctx.hint()));
        }

        //projections
        result.setProjections((ProjectionsSegment) visitProjections(ctx.projections()));
        //selectSpecification
        if (null != ctx.selectSpecification()) {
            result.getProjections().setDistinctRow(isDistinct(ctx));
        }

        //selectIntoExpression
        if (null != ctx.selectIntoExpression()) {
            result.setSelectIntoSegment((SelectIntoSegment) visitSelectIntoExpression(ctx.selectIntoExpression()));
        }

        // fromClause
        if (null != ctx.fromClause()) {
            if (null != ctx.fromClause().tableReferences()) {
                TableSegment tableSource = (TableSegment) visitTableReferences(ctx.fromClause().tableReferences());
                result.setFrom(tableSource);
            }
            if (null != ctx.fromClause().DUAL()) {
                TableSegment tableSource = new SimpleTableSegment(new TableNameSegment(ctx.fromClause().DUAL().getSymbol().getStartIndex(),
                        ctx.fromClause().DUAL().getSymbol().getStopIndex(), new IdentifierValue(ctx.fromClause().DUAL().getText())));
                result.setFrom(tableSource);
            }
        }
        // whereClause
        if (null != ctx.whereClause()) {
            result.setWhere((WhereSegment) visitWhereClause(ctx.whereClause()));
        }
        // groupByClause
        if (null != ctx.groupByClause()) {
            result.setGroupBy((GroupBySegment) visitGroupByClause(ctx.groupByClause()));
        }
        // havingClause
        if (null != ctx.havingClause()) {
            result.setHaving((HavingSegment) visitHavingClause(ctx.havingClause()));
        }
        // windowClause
        if (null != ctx.windowClause()) {
            result.setWindow((WindowSegment) visitWindowClause(ctx.windowClause()));
        }

        return result;
    }

    @Override
    public ASTNode visitWhereClause(final WhereClauseContext ctx) {
        ASTNode segment = visitExpr(ctx.expr());
        return new WhereSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ExpressionSegment) segment);
    }

    @Override
    public ASTNode visitGroupByClause(final GroupByClauseContext ctx) {
        Collection<OrderByItemSegment> items = new LinkedList<>();
        for (OrderByItemContext each : ctx.orderByItem()) {
            items.add((OrderByItemSegment) visitOrderByItem(each));
        }
        return new GroupBySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), items);
    }

    @Override
    public ASTNode visitLimitClause(final LimitClauseContext ctx) {
        if (null == ctx.limitOffset()) {
            return new LimitSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), null, (PaginationValueSegment) visitLimitRowCount(ctx.limitRowCount()));
        }
        PaginationValueSegment rowCount;
        PaginationValueSegment offset;
        if (null != ctx.OFFSET()) {
            rowCount = (PaginationValueSegment) visitLimitRowCount(ctx.limitRowCount());
            offset = (PaginationValueSegment) visitLimitOffset(ctx.limitOffset());
        } else {
            offset = (PaginationValueSegment) visitLimitOffset(ctx.limitOffset());
            rowCount = (PaginationValueSegment) visitLimitRowCount(ctx.limitRowCount());
        }
        return new LimitSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), offset, rowCount);
    }

    @Override
    public ASTNode visitLimitRowCount(final LimitRowCountContext ctx) {
        if (null != ctx.numberLiterals()) {
            return new NumberLiteralLimitValueSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ((NumberLiteralValue) visitNumberLiterals(ctx.numberLiterals())).getValue().longValue());
        }
        ParameterMarkerSegment result = new ParameterMarkerLimitValueSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(),
                ((ParameterMarkerValue) visitParameterMarker(ctx.parameterMarker())).getValue());
        getParameterMarkerSegments().add(result);
        return result;
    }

    @Override
    public ASTNode visitLimitOffset(final LimitOffsetContext ctx) {
        if (null != ctx.numberLiterals()) {
            return new NumberLiteralLimitValueSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ((NumberLiteralValue) visitNumberLiterals(ctx.numberLiterals())).getValue().longValue());
        }
        ParameterMarkerSegment result = new ParameterMarkerLimitValueSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(),
                ((ParameterMarkerValue) visitParameterMarker(ctx.parameterMarker())).getValue());
        getParameterMarkerSegments().add(result);
        return result;
    }

    @Override
    public ASTNode visitOrderByClause(final OrderByClauseContext ctx) {
        Collection<OrderByItemSegment> items = new LinkedList<>();
        for (OrderByItemContext each : ctx.orderByItem()) {
            items.add((OrderByItemSegment) visitOrderByItem(each));
        }
        return new OrderBySegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), items);
    }

    @Override
    public ASTNode visitOrderByItem(final OrderByItemContext ctx) {
        OrderDirection orderDirection;
        if (null != ctx.direction()) {
            orderDirection = null == ctx.direction().DESC() ? OrderDirection.ASC : OrderDirection.DESC;
        } else {
            orderDirection = OrderDirection.ASC;
        }
        if (null != ctx.numberLiterals()) {
            return new IndexOrderByItemSegment(ctx.numberLiterals().getStart().getStartIndex(), ctx.numberLiterals().getStop().getStopIndex(),
                    SQLUtils.getExactlyNumber(ctx.numberLiterals().getText(), 10).intValue(), orderDirection, null);
        } else {
            ASTNode expr = visitExpr(ctx.expr());
            if (expr instanceof ColumnSegment) {
                return new ColumnOrderByItemSegment((ColumnSegment) expr, orderDirection, null);
            } else {
                return new ExpressionOrderByItemSegment(ctx.expr().getStart().getStartIndex(),
                        ctx.expr().getStop().getStopIndex(), getOriginalText(ctx.expr()), orderDirection, null, (ExpressionSegment) expr);
            }
        }
    }

    @Override
    public ASTNode visitHavingClause(final HavingClauseContext ctx) {
        ExpressionSegment expr = (ExpressionSegment) visitExpr(ctx.expr());
        return new HavingSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), expr);
    }

    @Override
    public ASTNode visitWindowClause(final WindowClauseContext ctx) {
        WindowSegment result = new WindowSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        for (WindowItemContext each : ctx.windowItem()) {
            result.getItemSegments().add((WindowItemSegment) visitWindowItem(each));
        }
        return result;
    }

    @Override
    public ASTNode visitWindowItem(final WindowItemContext ctx) {
        WindowItemSegment result = new WindowItemSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        result.setWindowName(new IdentifierValue(ctx.identifier().getText()));
        if (null != ctx.windowSpecification().PARTITION()) {
            result.setPartitionListSegments(getExpressionsFromExprList(ctx.windowSpecification().expr()));
        }
        if (null != ctx.windowSpecification().orderByClause()) {
            result.setOrderBySegment((OrderBySegment) visitOrderByClause(ctx.windowSpecification().orderByClause()));
        }
        if (null != ctx.windowSpecification().frameClause()) {
            result.setFrameClause(new CommonExpressionSegment(ctx.windowSpecification().frameClause().start.getStartIndex(), ctx.windowSpecification().frameClause().stop.getStopIndex(),
                    getOriginalText(ctx.windowSpecification().frameClause())));
        }
        return result;
    }

    private Collection<ExpressionSegment> getExpressionsFromExprList(final List<ExprContext> exprList) {
        if (null == exprList) {
            return Collections.emptyList();
        }
        Collection<ExpressionSegment> result = new ArrayList<>(exprList.size());
        for (ExprContext each : exprList) {
            result.add((ExpressionSegment) visitExpr(each));
        }
        return result;
    }

    @Override
    public ASTNode visitTableReferences(final TableReferencesContext ctx) {
        TableSegment result = (TableSegment) visitTableReference(ctx.tableReference(0));
        if (ctx.tableReference().size() > 1) {
            for (int i = 1; i < ctx.tableReference().size(); i++) {
                result = generateJoinTableSourceFromEscapedTableReference(ctx.tableReference(i), result);
            }
        }
        return result;
    }

    private JoinTableSegment generateJoinTableSourceFromEscapedTableReference(final TableReferenceContext ctx, final TableSegment tableSegment) {
        JoinTableSegment result = new JoinTableSegment();
        result.setStartIndex(tableSegment.getStartIndex());
        result.setStopIndex(ctx.stop.getStopIndex());
        result.setLeft(tableSegment);
        result.setJoinType(JoinType.COMMA.name());
        result.setRight((TableSegment) visitTableReference(ctx));
        return result;
    }

    @Override
    public ASTNode visitTableReference(final TableReferenceContext ctx) {
        TableSegment result;
        TableSegment left;
        left = null == ctx.tableFactor() ? (TableSegment) visitEscapedTableReference(ctx.escapedTableReference()) : (TableSegment) visitTableFactor(ctx.tableFactor());
        for (JoinedTableContext each : ctx.joinedTable()) {
            left = visitJoinedTable(each, left);
        }
        result = left;
        return result;
    }

    @Override
    public ASTNode visitEscapedTableReference(final EscapedTableReferenceContext ctx) {
        TableSegment result;
        TableSegment left;
        left = (TableSegment) visitTableFactor(ctx.tableFactor());
        for (JoinedTableContext each : ctx.joinedTable()) {
            left = visitJoinedTable(each, left);
        }
        result = left;
        return result;
    }

    @Override
    public ASTNode visitTableFactor(final TableFactorContext ctx) {
        if (null != ctx.subquery()) {
            ImpalaSelectStatement subquery = (ImpalaSelectStatement) visitSubquery(ctx.subquery());
            SubquerySegment subquerySegment = new SubquerySegment(ctx.subquery().start.getStartIndex(), ctx.subquery().stop.getStopIndex(), subquery, getOriginalText(ctx.subquery()));
            SubqueryTableSegment result = new SubqueryTableSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), subquerySegment);
            if (null != ctx.alias()) {
                result.setAlias((AliasSegment) visitAlias(ctx.alias()));
            }
            return result;
        }
        if (null != ctx.tableName()) {
            SimpleTableSegment result = (SimpleTableSegment) visitTableName(ctx.tableName());
            if (null != ctx.alias()) {
                result.setAlias((AliasSegment) visitAlias(ctx.alias()));
            }
            return result;
        }
        return visitTableReferences(ctx.tableReferences());
    }

    private JoinTableSegment visitJoinedTable(final JoinedTableContext ctx, final TableSegment tableSegment) {
        JoinTableSegment result = new JoinTableSegment();
        result.setLeft(tableSegment);
        result.setStartIndex(tableSegment.getStartIndex());
        result.setStopIndex(ctx.stop.getStopIndex());
        result.setJoinType(getJoinType(ctx));
        result.setNatural(null != ctx.naturalJoinType());
        TableSegment right = null == ctx.tableFactor() ? (TableSegment) visitTableReference(ctx.tableReference()) : (TableSegment) visitTableFactor(ctx.tableFactor());
        result.setRight(right);
        return null == ctx.joinSpecification() ? result : visitJoinSpecification(ctx.joinSpecification(), result);
    }

    private String getJoinType(final JoinedTableContext ctx) {
        if (null != ctx.innerJoinType()) {
            return JoinType.INNER.name();
        }
        if (null != ctx.outerJoinType()) {
            return null == ctx.outerJoinType().LEFT() ? JoinType.RIGHT.name() : JoinType.LEFT.name();
        }
        if (null != ctx.naturalJoinType()) {
            return getNaturalJoinType(ctx.naturalJoinType());
        }
        return JoinType.COMMA.name();
    }

    private String getNaturalJoinType(final NaturalJoinTypeContext ctx) {
        if (null != ctx.LEFT()) {
            return JoinType.LEFT.name();
        } else if (null != ctx.RIGHT()) {
            return JoinType.RIGHT.name();
        } else {
            return JoinType.INNER.name();
        }
    }

    private JoinTableSegment visitJoinSpecification(final JoinSpecificationContext ctx, final JoinTableSegment result) {
        if (null != ctx.expr()) {
            ExpressionSegment condition = (ExpressionSegment) visitExpr(ctx.expr());
            result.setCondition(condition);
        }
        if (null != ctx.USING()) {
            result.setUsing(ctx.columnNames().columnName().stream().map(each -> (ColumnSegment) visitColumnName(each)).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public ASTNode visitProjections(ProjectionsContext ctx) {
        ProjectionsSegment result = new ProjectionsSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());

        List<ProjectionSegment> projectionSegments = new LinkedList<>();
        //unqualifiedShorthand
        if (ctx.unqualifiedShorthand() != null) {
            ShorthandProjectionSegment shorthandProjectionSegment = new ShorthandProjectionSegment(ctx.unqualifiedShorthand().getStart().getStartIndex(), ctx.unqualifiedShorthand().getStop().getStopIndex());
            projectionSegments.add(shorthandProjectionSegment);
        }
        //projection
        ctx.projection().forEach(projectionContext -> {
            projectionSegments.add((ProjectionSegment) visitProjection(projectionContext));
        });

        result.getProjections().addAll(projectionSegments);
        return result;
    }

    @Override
    public ASTNode visitProjection(ProjectionContext ctx) {
        // FIXME :The stop index of project is the stop index of projection, instead of alias.
        if (null != ctx.qualifiedShorthand()) {
            return createShorthandProjection(ctx.qualifiedShorthand());
        }
        AliasSegment alias = null == ctx.alias() ? null : (AliasSegment) visitAlias(ctx.alias());
        ASTNode exprProjection = visitExpr(ctx.expr());
        if (exprProjection instanceof ColumnSegment) {
            ColumnProjectionSegment result = new ColumnProjectionSegment((ColumnSegment) exprProjection);
            result.setAlias(alias);
            return result;
        }
        if (exprProjection instanceof SubquerySegment) {
            SubquerySegment subquerySegment = (SubquerySegment) exprProjection;
            String text = ctx.start.getInputStream().getText(new Interval(subquerySegment.getStartIndex(), subquerySegment.getStopIndex()));
            SubqueryProjectionSegment result = new SubqueryProjectionSegment((SubquerySegment) exprProjection, text);
            result.setAlias(alias);
            return result;
        }
        if (exprProjection instanceof ExistsSubqueryExpression) {
            ExistsSubqueryExpression existsSubqueryExpression = (ExistsSubqueryExpression) exprProjection;
            String text = ctx.start.getInputStream().getText(new Interval(existsSubqueryExpression.getStartIndex(), existsSubqueryExpression.getStopIndex()));
            SubqueryProjectionSegment result = new SubqueryProjectionSegment(((ExistsSubqueryExpression) exprProjection).getSubquery(), text);
            result.setAlias(alias);
            return result;
        }
        return createProjection(ctx, alias, exprProjection);
    }

    private ShorthandProjectionSegment createShorthandProjection(final QualifiedShorthandContext shorthand) {
        ShorthandProjectionSegment result = new ShorthandProjectionSegment(shorthand.getStart().getStartIndex(), shorthand.getStop().getStopIndex());
        IdentifierContext identifier = shorthand.identifier().get(shorthand.identifier().size() - 1);
        OwnerSegment owner = new OwnerSegment(identifier.getStart().getStartIndex(), identifier.getStop().getStopIndex(), new IdentifierValue(identifier.getText()));
        result.setOwner(owner);
        if (shorthand.identifier().size() > 1) {
            IdentifierContext databaseIdentifier = shorthand.identifier().get(0);
            owner.setOwner(new OwnerSegment(databaseIdentifier.getStart().getStartIndex(), databaseIdentifier.getStop().getStopIndex(), new IdentifierValue(databaseIdentifier.getText())));
        }
        return result;
    }

    @Override
    public ASTNode visitAlias(final AliasContext ctx) {
        return new AliasSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), new IdentifierValue(ctx.identifier().getText()));
    }

    private ASTNode createProjection(final ProjectionContext ctx, final AliasSegment alias, final ASTNode projection) {
        if (projection instanceof AggregationProjectionSegment) {
            ((AggregationProjectionSegment) projection).setAlias(alias);
            return projection;
        }
        if (projection instanceof ExpressionProjectionSegment) {
            ((ExpressionProjectionSegment) projection).setAlias(alias);
            return projection;
        }
        if (projection instanceof FunctionSegment) {
            FunctionSegment functionSegment = (FunctionSegment) projection;
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(functionSegment.getStartIndex(), functionSegment.getStopIndex(), functionSegment.getText(), functionSegment);
            result.setAlias(alias);
            return result;
        }
        if (projection instanceof CommonExpressionSegment) {
            CommonExpressionSegment segment = (CommonExpressionSegment) projection;
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(segment.getStartIndex(), segment.getStopIndex(), segment.getText(), segment);
            result.setAlias(alias);
            return result;
        }
        // FIXME :For DISTINCT()
        if (projection instanceof ColumnSegment) {
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx), (ColumnSegment) projection);
            result.setAlias(alias);
            return result;
        }
        if (projection instanceof SubqueryExpressionSegment) {
            SubqueryExpressionSegment subqueryExpressionSegment = (SubqueryExpressionSegment) projection;
            String text = ctx.start.getInputStream().getText(new Interval(subqueryExpressionSegment.getStartIndex(), subqueryExpressionSegment.getStopIndex()));
            SubqueryProjectionSegment result = new SubqueryProjectionSegment(subqueryExpressionSegment.getSubquery(), text);
            result.setAlias(alias);
            return result;
        }
        if (projection instanceof BinaryOperationExpression) {
            int startIndex = ((BinaryOperationExpression) projection).getStartIndex();
            int stopIndex = null == alias ? ((BinaryOperationExpression) projection).getStopIndex() : alias.getStopIndex();
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(startIndex, stopIndex, ((BinaryOperationExpression) projection).getText(), (BinaryOperationExpression) projection);
            result.setAlias(alias);
            return result;
        }
        if (projection instanceof ParameterMarkerExpressionSegment) {
            ParameterMarkerExpressionSegment result = (ParameterMarkerExpressionSegment) projection;
            result.setAlias(alias);
            return projection;
        }
        if (projection instanceof CaseWhenExpression) {
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx.expr()), (CaseWhenExpression) projection);
            result.setAlias(alias);
            return result;
        }
        if (projection instanceof VariableSegment) {
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx.expr()), (VariableSegment) projection);
            result.setAlias(alias);
            return result;
        }
        if (projection instanceof BetweenExpression) {
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx.expr()), (BetweenExpression) projection);
            result.setAlias(alias);
            return result;
        }
        if (projection instanceof InExpression) {
            ExpressionProjectionSegment result = new ExpressionProjectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx.expr()), (InExpression) projection);
            result.setAlias(alias);
            return result;
        }
        ExpressionSegment column = (ExpressionSegment) projection;
        ExpressionProjectionSegment result = null == alias
                ? new ExpressionProjectionSegment(column.getStartIndex(), column.getStopIndex(), String.valueOf(column.getText()), column)
                : new ExpressionProjectionSegment(column.getStartIndex(), ctx.alias().stop.getStopIndex(), String.valueOf(column.getText()), column);
        result.setAlias(alias);
        return result;
    }

    @Override
    public ASTNode visitIntervalExpression(IntervalExpressionContext ctx) {
        FunctionSegment result = new FunctionSegment(ctx.INTERVAL().getSymbol().getStartIndex(), ctx.INTERVAL().getSymbol().getStopIndex(), ctx.INTERVAL().getText(), ctx.INTERVAL().getText());
        result.getParameters().add((ExpressionSegment) visit(ctx.intervalValue().expr()));
        result.getParameters().add(new LiteralExpressionSegment(ctx.intervalValue().intervalUnit().getStart().getStartIndex(), ctx.intervalValue().intervalUnit().getStop().getStopIndex(),
                getOriginalText(ctx.intervalValue().intervalUnit())));
        return result;
    }

    @Override
    public ASTNode visitFunctionCall(FunctionCallContext ctx) {
        if (null != ctx.aggregationFunction()) {
            return visitAggregationFunction(ctx.aggregationFunction());
        }
        if (null != ctx.specialFunction()) {
            return visitSpecialFunction(ctx.specialFunction());
        }
        if (null != ctx.regularFunction()) {
            return visitRegularFunction(ctx.regularFunction());
        }
        if (null != ctx.jsonFunction()) {
            return visitJsonFunction(ctx.jsonFunction());
        }
        if (null != ctx.udfFunction()) {
            return visitUdfFunction(ctx.udfFunction());
        }
        throw new IllegalStateException("FunctionCallContext must have aggregationFunction, regularFunction, specialFunction, jsonFunction or udfFunction.");
    }

    @Override
    public ASTNode visitAggregationFunction(AggregationFunctionContext ctx) {
        String aggregationType = ctx.aggregationFunctionName().getText();
        return AggregationType.isAggregationType(aggregationType)
                ? createAggregationSegment(ctx, aggregationType)
                : new ExpressionProjectionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getOriginalText(ctx));
    }

    private ASTNode createAggregationSegment(AggregationFunctionContext ctx, String aggregationType) {
        AggregationType type = AggregationType.valueOf(aggregationType.toUpperCase());
        String innerExpression = ctx.start.getInputStream().getText(new Interval(ctx.LP_().getSymbol().getStartIndex(), ctx.stop.getStopIndex()));
        if (null != ctx.distinct()) {
            AggregationDistinctProjectionSegment result = new AggregationDistinctProjectionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(),
                    type, innerExpression, getDistinctExpression(ctx));
            result.getParameters().addAll(getExpressions(ctx));
            return result;
        }
        AggregationProjectionSegment result = new AggregationProjectionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), type, innerExpression);
        result.getParameters().addAll(getExpressions(ctx));
        return result;
    }

    private String getDistinctExpression(final AggregationFunctionContext ctx) {
        StringBuilder result = new StringBuilder();
        for (int i = 3; i < ctx.getChildCount() - 1; i++) {
            result.append(ctx.getChild(i).getText());
        }
        return result.toString();
    }

    private Collection<ExpressionSegment> getExpressions(final AggregationFunctionContext ctx) {
        if (null == ctx.expr()) {
            return Collections.emptyList();
        }
        Collection<ExpressionSegment> result = new LinkedList<>();
        for (ExprContext exprContext : ctx.expr()) {
            result.add((ExpressionSegment) visitExpr(exprContext));
        }
        return result;
    }

    @Override
    public ASTNode visitSpecialFunction(final SpecialFunctionContext ctx) {
        if (null != ctx.groupConcatFunction()) {
            return visitGroupConcatFunction(ctx.groupConcatFunction());
        }
        if (null != ctx.windowFunction()) {
            return visitWindowFunction(ctx.windowFunction());
        }
        if (null != ctx.castFunction()) {
            return visitCastFunction(ctx.castFunction());
        }
        if (null != ctx.convertFunction()) {
            return visitConvertFunction(ctx.convertFunction());
        }
        if (null != ctx.positionFunction()) {
            return visitPositionFunction(ctx.positionFunction());
        }
        if (null != ctx.substringFunction()) {
            return visitSubstringFunction(ctx.substringFunction());
        }
        if (null != ctx.extractFunction()) {
            return visitExtractFunction(ctx.extractFunction());
        }
        if (null != ctx.charFunction()) {
            return visitCharFunction(ctx.charFunction());
        }
        if (null != ctx.trimFunction()) {
            return visitTrimFunction(ctx.trimFunction());
        }
        if (null != ctx.weightStringFunction()) {
            return visitWeightStringFunction(ctx.weightStringFunction());
        }
        if (null != ctx.valuesFunction()) {
            return visitValuesFunction(ctx.valuesFunction());
        }
        if (null != ctx.currentUserFunction()) {
            return visitCurrentUserFunction(ctx.currentUserFunction());
        }
        return new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getOriginalText(ctx), getOriginalText(ctx));
    }

    @Override
    public ASTNode visitGroupConcatFunction(final GroupConcatFunctionContext ctx) {
        calculateParameterCount(ctx.expr());
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.GROUP_CONCAT().getText(), getOriginalText(ctx));
        for (ExprContext exprContext : ctx.expr()) {
            result.getParameters().add((ExpressionSegment) visitExpr(exprContext));
        }
        return result;
    }

    @Override
    public ASTNode visitWindowFunction(final WindowFunctionContext ctx) {
        super.visitWindowFunction(ctx);
        return new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.funcName.getText(), getOriginalText(ctx));
    }

    @Override
    public ASTNode visitCastFunction(final CastFunctionContext ctx) {
        calculateParameterCount(ctx.expr());
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.CAST().getText(), getOriginalText(ctx));
        for (ExprContext exprContext : ctx.expr()) {
            ASTNode expr = visitExpr(exprContext);
            if (expr instanceof ColumnSegment) {
                result.getParameters().add((ColumnSegment) expr);
            } else if (expr instanceof LiteralExpressionSegment) {
                result.getParameters().add((LiteralExpressionSegment) expr);
            }
        }
        if (null != ctx.castType()) {
            result.getParameters().add((DataTypeSegment) visitCastType(ctx.castType()));
        }
        if (null != ctx.DATETIME()) {
            DataTypeSegment dataType = new DataTypeSegment();
            dataType.setDataTypeName(ctx.DATETIME().getText());
            dataType.setStartIndex(ctx.DATETIME().getSymbol().getStartIndex());
            dataType.setStopIndex(ctx.DATETIME().getSymbol().getStopIndex());
            if (null != ctx.typeDatetimePrecision()) {
                dataType.setDataLength((DataTypeLengthSegment) visitTypeDatetimePrecision(ctx.typeDatetimePrecision()));
            }
            result.getParameters().add(dataType);
        }
        return result;
    }

    @Override
    public ASTNode visitTypeDatetimePrecision(final TypeDatetimePrecisionContext ctx) {
        DataTypeLengthSegment result = new DataTypeLengthSegment();
        result.setStartIndex(ctx.start.getStartIndex());
        result.setStopIndex(ctx.stop.getStartIndex());
        result.setPrecision(Integer.parseInt(ctx.number().getText()));
        return result;
    }

    @Override
    public ASTNode visitCastType(final CastTypeContext ctx) {
        DataTypeSegment result = new DataTypeSegment();
        result.setDataTypeName(getDataTypeName(ctx.dataType()));
        result.setStartIndex(ctx.start.getStartIndex());
        result.setStopIndex(ctx.stop.getStopIndex());

        DataTypeLengthSegment dataTypeLengthSegment1 = getDataTypeLengthSegmentByFieldLength(ctx.dataType());
        if (dataTypeLengthSegment1 != null) {
            result.setDataLength(dataTypeLengthSegment1);
        }
        DataTypeLengthSegment dataTypeLengthSegment2 = getDataTypeLengthSegmentByPrecision(ctx.dataType());
        if (dataTypeLengthSegment2 != null) {
            result.setDataLength(dataTypeLengthSegment2);
        }
        return result;
    }

    protected String getDataTypeName(DataTypeContext dataTypeContext) {
        if (dataTypeContext.simpleDataType() != null) {
            return dataTypeContext.simpleDataType().dtname.getText();
        } else {
            ComplexTypeContext complexTypeContext = dataTypeContext.complexType();
            if (complexTypeContext.structType() != null) {
                return complexTypeContext.structType().ctname.getText();
            } else if (complexTypeContext.arrayType() != null) {
                return complexTypeContext.arrayType().ctname.getText();
            } else {
                return complexTypeContext.mapType().ctname.getText();
            }
        }
    }

    protected DataTypeLengthSegment getDataTypeLengthSegmentByFieldLength(DataTypeContext dataTypeContext) {
        if (dataTypeContext.simpleDataType() != null) {
            SimpleDataTypeContext simpleDataTypeContext = dataTypeContext.simpleDataType();
            if (simpleDataTypeContext.CHAR() != null || simpleDataTypeContext.VARCHAR() != null) {
                if (simpleDataTypeContext.INTEGER_().size() == 1) {
                    DataTypeLengthSegment result = new DataTypeLengthSegment();
                    result.setStartIndex(simpleDataTypeContext.INTEGER_(0).getSymbol().getStartIndex());
                    result.setStartIndex(simpleDataTypeContext.INTEGER_(0).getSymbol().getStopIndex());
                    result.setPrecision(new BigDecimal(simpleDataTypeContext.INTEGER_(0).getText()).intValue());
                    return result;
                }
            }
        }
        return null;
    }

    protected DataTypeLengthSegment getDataTypeLengthSegmentByPrecision(DataTypeContext dataTypeContext) {
        if (dataTypeContext.simpleDataType() != null) {
            SimpleDataTypeContext simpleDataTypeContext = dataTypeContext.simpleDataType();
            if (simpleDataTypeContext.prec != null) {
                DataTypeLengthSegment result = new DataTypeLengthSegment();
                result.setStartIndex(simpleDataTypeContext.prec.getStartIndex());
                result.setStopIndex(simpleDataTypeContext.prec.getStopIndex());
                result.setPrecision(Integer.parseInt(simpleDataTypeContext.prec.getText()));
                if (simpleDataTypeContext.scale != null) {
                    result.setStopIndex(simpleDataTypeContext.scale.getStopIndex());
                    result.setScale(Integer.parseInt(simpleDataTypeContext.scale.getText()));
                }
                return result;
            }
        }
        return null;
    }

    @Override
    public ASTNode visitConvertFunction(final ConvertFunctionContext ctx) {
        calculateParameterCount(Collections.singleton(ctx.expr()));
        return new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.CONVERT().getText(), getOriginalText(ctx));
    }

    @Override
    public ASTNode visitPositionFunction(final PositionFunctionContext ctx) {
        calculateParameterCount(ctx.expr());
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.POSITION().getText(), getOriginalText(ctx));
        result.getParameters().add((ExpressionSegment) visitExpr(ctx.expr(0)));
        result.getParameters().add((ExpressionSegment) visitExpr(ctx.expr(1)));
        return result;
    }

    @Override
    public ASTNode visitSubstringFunction(final SubstringFunctionContext ctx) {
        FunctionSegment result = new FunctionSegment(
                ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), null == ctx.SUBSTR() ? ctx.SUBSTRING().getText() : ctx.SUBSTR().getText(), getOriginalText(ctx));
        result.getParameters().add((ExpressionSegment) visitExpr(ctx.expr()));
        for (NumberContext each : ctx.number()) {
            result.getParameters().add(new LiteralExpressionSegment(each.start.getStartIndex(), each.start.getStopIndex(), new NumberLiteralValue(each.getText()).getValue()));
        }
        return result;
    }

    @Override
    public ASTNode visitExtractFunction(final ExtractFunctionContext ctx) {
        calculateParameterCount(Collections.singleton(ctx.expr()));
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.EXTRACT().getText(), getOriginalText(ctx));
        result.getParameters().add(new LiteralExpressionSegment(ctx.identifier().getStart().getStartIndex(), ctx.identifier().getStop().getStopIndex(), ctx.identifier().getText()));
        result.getParameters().add((ExpressionSegment) visitExpr(ctx.expr()));
        return result;
    }

    @Override
    public ASTNode visitCharFunction(final CharFunctionContext ctx) {
        calculateParameterCount(ctx.expr());
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.CHAR().getText(), getOriginalText(ctx));
        for (ExprContext each : ctx.expr()) {
            ASTNode expr = visitExpr(each);
            result.getParameters().add((ExpressionSegment) expr);
        }
        return result;
    }

    @Override
    public ASTNode visitTrimFunction(final TrimFunctionContext ctx) {
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.TRIM().getText(), getOriginalText(ctx));
        if (null != ctx.BOTH()) {
            result.getParameters().add(new LiteralExpressionSegment(ctx.BOTH().getSymbol().getStartIndex(), ctx.BOTH().getSymbol().getStopIndex(),
                    new OtherLiteralValue(ctx.BOTH().getSymbol().getText()).getValue()));
        }
        if (null != ctx.TRAILING()) {
            result.getParameters().add(new LiteralExpressionSegment(ctx.TRAILING().getSymbol().getStartIndex(), ctx.TRAILING().getSymbol().getStopIndex(),
                    new OtherLiteralValue(ctx.TRAILING().getSymbol().getText()).getValue()));
        }
        if (null != ctx.LEADING()) {
            result.getParameters().add(new LiteralExpressionSegment(ctx.LEADING().getSymbol().getStartIndex(), ctx.LEADING().getSymbol().getStopIndex(),
                    new OtherLiteralValue(ctx.LEADING().getSymbol().getText()).getValue()));
        }
        for (ExprContext each : ctx.expr()) {
            result.getParameters().add((ExpressionSegment) visitExpr(each));
        }
        return result;
    }

    @Override
    public ASTNode visitWeightStringFunction(final WeightStringFunctionContext ctx) {
        calculateParameterCount(Collections.singleton(ctx.expr()));
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.WEIGHT_STRING().getText(), getOriginalText(ctx));
        result.getParameters().add((ExpressionSegment) visitExpr(ctx.expr()));
        return result;
    }

    @Override
    public ASTNode visitValuesFunction(final ValuesFunctionContext ctx) {
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.VALUES().getText(), getOriginalText(ctx));
        if (!ctx.columnNames().columnName().isEmpty()) {
            ColumnSegment columnSegment = (ColumnSegment) visitColumnName(ctx.columnNames().columnName(0));
            result.getParameters().add(columnSegment);
        }
        return result;
    }

    @Override
    public ASTNode visitCurrentUserFunction(final CurrentUserFunctionContext ctx) {
        return new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.CURRENT_USER().getText(), getOriginalText(ctx));
    }

    @Override
    public ASTNode visitRegularFunction(final RegularFunctionContext ctx) {
        return null == ctx.completeRegularFunction() ? visitShorthandRegularFunction(ctx.shorthandRegularFunction()) : visitCompleteRegularFunction(ctx.completeRegularFunction());
    }

    @Override
    public ASTNode visitCompleteRegularFunction(final CompleteRegularFunctionContext ctx) {
        FunctionSegment result = new FunctionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.regularFunctionName().getText(), getOriginalText(ctx));
        Collection<ExpressionSegment> expressionSegments = ctx.expr().stream().map(each -> (ExpressionSegment) visitExpr(each)).collect(Collectors.toList());
        result.getParameters().addAll(expressionSegments);
        return result;
    }

    @Override
    public ASTNode visitShorthandRegularFunction(final ShorthandRegularFunctionContext ctx) {
        String text = getOriginalText(ctx);
        FunctionSegment result;
        if (null != ctx.CURRENT_TIME()) {
            result = new FunctionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.CURRENT_TIME().getText(), text);
            if (null != ctx.number()) {
                result.getParameters().add(new LiteralExpressionSegment(ctx.number().start.getStartIndex(), ctx.number().stop.getStopIndex(),
                        new NumberLiteralValue(ctx.number().getText())));
            }
        } else {
            result = new FunctionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx), text);
        }
        return result;
    }

    @Override
    public ASTNode visitJsonFunction(final JsonFunctionContext ctx) {
        JsonFunctionNameContext functionNameContext = ctx.jsonFunctionName();
        String functionName;
        if (null != functionNameContext) {
            functionName = functionNameContext.getText();
            for (ExprContext each : ctx.expr()) {
                visitExpr(each);
            }
        } else if (null != ctx.JSON_SEPARATOR()) {
            functionName = ctx.JSON_SEPARATOR().getText();
        } else {
            functionName = ctx.JSON_UNQUOTED_SEPARATOR().getText();
        }
        return new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), functionName, getOriginalText(ctx));
    }

    @Override
    public ASTNode visitUdfFunction(final UdfFunctionContext ctx) {
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getOriginalText(ctx), getOriginalText(ctx));
        if (null != ctx.expr()) {
            for (ExprContext each : ctx.expr()) {
                result.getParameters().add((ExpressionSegment) visitExpr(each));
            }
        }
        return result;
    }

    @Override
    public ASTNode visitCollateClause(final CollateClauseContext ctx) {
        if (null != ctx.collationName()) {
            return new LiteralExpressionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.collationName().textOrIdentifier().getText());
        }
        ParameterMarkerExpressionSegment result = new ParameterMarkerExpressionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(),
                ((ParameterMarkerValue) visitParameterMarker(ctx.parameterMarker())).getValue());
        getParameterMarkerSegments().add(result);
        return result;
    }

    @Override
    public ASTNode visitMatchExpression(final MatchExpressionContext ctx) {
        visitExpr(ctx.expr());
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new CommonExpressionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), text);
    }

    @Override
    public ASTNode visitCaseExpression(final CaseExpressionContext ctx) {
        Collection<ExpressionSegment> whenExprs = new LinkedList<>();
        Collection<ExpressionSegment> thenExprs = new LinkedList<>();
        for (CaseWhenContext each : ctx.caseWhen()) {
            whenExprs.add((ExpressionSegment) visitExpr(each.expr(0)));
            thenExprs.add((ExpressionSegment) visitExpr(each.expr(1)));
        }
        ExpressionSegment caseExpr = null == ctx.simpleExpr() ? null : (ExpressionSegment) visitSimpleExpr(ctx.simpleExpr());
        ExpressionSegment elseExpr = null == ctx.caseElse() ? null : (ExpressionSegment) visitExpr(ctx.caseElse().expr());
        return new CaseWhenExpression(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), caseExpr, whenExprs, thenExprs, elseExpr);
    }

    @Override
    public ASTNode visitVariable(final VariableContext ctx) {
        return null == ctx.systemVariable() ? visitUserVariable(ctx.userVariable()) : visitSystemVariable(ctx.systemVariable());
    }

    @Override
    public ASTNode visitUserVariable(final UserVariableContext ctx) {
        return new VariableSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.textOrIdentifier().getText());
    }

    @Override
    public ASTNode visitSystemVariable(final SystemVariableContext ctx) {
        VariableSegment result = new VariableSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.rvalueSystemVariable().getText());
        if (null != ctx.systemVariableScope) {
            result.setScope(ctx.systemVariableScope.getText());
        }
        return result;
    }

    @Override
    public ASTNode visitUpdate(UpdateContext ctx) {
        ImpalaUpdateStatement result = new ImpalaUpdateStatement();
        //hint
        if (ctx.hint() != null) {
            result.setHint((HintSegment) visitHint(ctx.hint()));
        }
        //tableName
        TableNameContext tableName = ctx.tableName();
        result.setTable((TableSegment) visitTableName(tableName));

        //setAssignmentsClause
        result.setSetAssignment((SetAssignmentSegment) visitSetAssignmentsClause(ctx.setAssignmentsClause()));

        //fromClause
        if (null != ctx.fromClause()) {
            result.setFrom((TableSegment) visitFromClause(ctx.fromClause()));
        }

        //whereClause
        if (null != ctx.whereClause()) {
            result.setWhere((WhereSegment) visitWhereClause(ctx.whereClause()));
        }

        return result;
    }

    @Override
    public ASTNode visitFromClause(FromClauseContext ctx) {
        if (null != ctx.tableReferences()) {
            return visitTableReferences(ctx.tableReferences());
        } else {
            return new SimpleTableSegment(new TableNameSegment(ctx.DUAL().getSymbol().getStartIndex(),
                    ctx.DUAL().getSymbol().getStopIndex(), new IdentifierValue(ctx.DUAL().getText())));
        }
    }

    @Override
    public ASTNode visitSetAssignmentsClause(SetAssignmentsClauseContext ctx) {
        Collection<ColumnAssignmentSegment> assignments = new LinkedList<>();
        for (AssignmentContext each : ctx.assignment()) {
            assignments.add((ColumnAssignmentSegment) visitAssignment(each));
        }
        return new SetAssignmentSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), assignments);
    }

    @Override
    public ASTNode visitAssignment(final AssignmentContext ctx) {
        ColumnSegment column = (ColumnSegment) visitColumnName(ctx.columnName());
        ExpressionSegment value = (ExpressionSegment) visitAssignmentValue(ctx.assignmentValue());
        List<ColumnSegment> columnSegments = new LinkedList<>();
        columnSegments.add(column);
        return new ColumnAssignmentSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), columnSegments, value);
    }

    @Override
    public ASTNode visitBlobValue(final BlobValueContext ctx) {
        return new StringLiteralValue(ctx.STRING_().getText());
    }

    @Override
    public ASTNode visitInsert(InsertContext ctx) {
        ImpalaInsertStatement result = new ImpalaInsertStatement();
        //with
        if (null != ctx.withClause()) {
            result.setWith((WithSegment) visitWithClause(ctx.withClause()));
        }
        //hint
        if (null != ctx.insertHintClause(0)) {
            result.setHint((HintSegment) visitInsertHintClause(ctx.insertHintClause(0)));
        }
        //tableName
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        //columnNames
        if (ctx.columnNames() != null) {
            result.setInsertColumns(new InsertColumnsSegment(ctx.LP_().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex(), getInsertColumns(ctx.columnNames())));
        } else {
            int startIndex = ctx.tableName().stop.getStopIndex() + 1;
            int stopIndex = startIndex;
            result.setInsertColumns(new InsertColumnsSegment(startIndex, stopIndex, Collections.emptyList()));
        }

        //insertPartitionClause
        if (ctx.insertPartitionClause() != null) {
            InsertPartitionClauseContext insertPartitionClause = ctx.insertPartitionClause();
            result.setPartitionProperties(createCommonPartitionPropertiesSegment(insertPartitionClause.partitionSpec(), insertPartitionClause.PARTITION().getSymbol().getStartIndex(), insertPartitionClause.RP_().getSymbol().getStopIndex()));
        }

        //select
        if (ctx.select() != null) {
            result.setInsertSelect(getInsertSelectSegment(ctx.select()));
        } else {
            //values
            result.getValues().addAll(getInsertValuesSegments(ctx.insertValuesClause()));
        }

        result.addParameterMarkerSegments(getParameterMarkerSegments());

        return result;
    }

    public PartitionPropertiesSegment createCommonPartitionPropertiesSegment(List<PartitionSpecContext> partitionSpecs, int outerStartIndex, int outerStopIndex) {
        PartitionPropertiesSegment result = new PartitionPropertiesSegment(outerStartIndex, outerStopIndex);
        Collection<ColumnSegment> allColumns = getAllPartitionColumns(partitionSpecs);

        result.getPartitionColumns().addAll(allColumns);
        return result;
    }

    private Collection<ColumnSegment> getAllPartitionColumns(List<PartitionSpecContext> partitionSpecs) {
        Collection<ColumnSegment> result = new LinkedList<>();

        for (PartitionSpecContext partitionSpec : partitionSpecs) {
            result.addAll(getColumnsByPartitionSpec(partitionSpec));
        }
        return result;
    }

    private Collection<ColumnSegment> getColumnsByPartitionSpec(PartitionSpecContext partitionSpec) {
        if (partitionSpec.simplePartitionSpec() != null) {
            NameContext name = partitionSpec.simplePartitionSpec().name();
            return List.of(new ColumnSegment(name.start.getStartIndex(), name.stop.getStopIndex(), new IdentifierValue(name.getText())));
        } else {
            return extractColumnsComplex(partitionSpec.complexPartitionSpec().comparisonExpressionOnPartitionCol());
        }
    }

    private Collection<ColumnSegment> extractColumnsComplex(ComparisonExpressionOnPartitionColContext ceopc) {
        Collection<ColumnSegment> result = new LinkedList<>();
        if (ceopc.comparisonOperator() != null || ceopc.BETWEEN() != null) {
            result.add(new ColumnSegment(ceopc.name().start.getStartIndex(), ceopc.name().stop.getStopIndex(), new IdentifierValue(ceopc.name().getText())));
            return result;
        } else if (ceopc.LP_() != null) {
            for (ComparisonExpressionOnPartitionColContext each : ceopc.comparisonExpressionOnPartitionCol()) {
                result.addAll(extractColumnsComplex(each));
            }
            return result;
        } else if (ceopc.logicOp != null) {
            result.addAll(extractColumnsComplex(ceopc.left));
            result.addAll(extractColumnsComplex(ceopc.right));
            return result;
        } else {
            NameContext name = ceopc.simplePartitionSpec().name();
            result.add(new ColumnSegment(name.start.getStartIndex(), name.stop.getStopIndex(), new IdentifierValue(name.getText())));
            return result;
        }
    }

    private Collection<InsertValuesSegment> getInsertValuesSegments(InsertValuesClauseContext insertValuesClause) {
        Collection<InsertValuesSegment> result = new LinkedList<>();

        List<AssignmentValuesContext> assignmentValuesContexts = insertValuesClause.rowConstructorList().assignmentValues();
        for (AssignmentValuesContext each : assignmentValuesContexts) {
            result.add((InsertValuesSegment) visitAssignmentValues(each));
        }
        return result;
    }

    private SubquerySegment getInsertSelectSegment(SelectContext select) {
        ImpalaSelectStatement selectStatement = (ImpalaSelectStatement) visitSelect(select);
        return new SubquerySegment(select.start.getStartIndex(), select.stop.getStopIndex(), selectStatement, getOriginalText(select));
    }

    private Collection<ColumnSegment> getInsertColumns(ColumnNamesContext columnNames) {
        Collection<ColumnSegment> result = new LinkedList<>();
        for (ColumnNameContext columnName : columnNames.columnName()) {
            result.add((ColumnSegment) visitColumnName(columnName));
        }
        return result;
    }


    @Override
    public ASTNode visitInsertHintClause(InsertHintClauseContext ctx) {
        HintSegment result = new HintSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.getText());
        if (null != ctx.hintWithDashes()) {
            result.setHintSegmentType(HintSegmentType.INLINE_HINT);
        } else if (null != ctx.hintWithCstyleComments()) {
            result.setHintSegmentType(HintSegmentType.BLOCK_HINT);
        } else {
            result.setHintSegmentType(HintSegmentType.BRACKETS_HINT);
        }
        return result;
    }

    @Override
    public ASTNode visitDelete(DeleteContext ctx) {
        ImpalaDeleteStatement result = new ImpalaDeleteStatement();
        //hint
        if (ctx.hint() != null) {
            result.setHint((HintSegment) visitHint(ctx.hint()));
        }
        //delete 表名
        if (ctx.tableName() != null) {
            result.setTable((TableSegment) visitTableName(ctx.tableName()));
        } else{
            //delete 表引用
            result.setTable((TableSegment) visitTableReference(ctx.tableReference()));
        }

        //whereClause
        if (ctx.whereClause() != null) {
            result.setWhere((WhereSegment) visitWhereClause(ctx.whereClause()));
        }
        //joined table more
        if (ctx.multipleJoinedTable() != null) {
            result.setJoinTable((JoinTableSegment) visitMultipleJoinedTable(ctx.multipleJoinedTable()));
        }

        return result;
    }

    @Override
    public ASTNode visitMultipleJoinedTable(MultipleJoinedTableContext ctx) {
        JoinTableSegment result;
        TableSegment left;
        left = (TableSegment) visitTableFactor(ctx.tableFactor());

        //必须要有joinTable
        if (ctx.joinedTable().isEmpty()) {
            return null;
        }

        for (JoinedTableContext each : ctx.joinedTable()) {
            left = visitJoinedTable(each, left);
        }
        result = (JoinTableSegment) left;
        return result;
    }

    @Override
    public ASTNode visitLoadData(LoadDataContext ctx) {
        ImpalaLoadDataStatement result = new ImpalaLoadDataStatement();
        //tableName
        result.setTableSegment((SimpleTableSegment) visitTableName(ctx.tableName()));
        //partition
        if (ctx.loadDataPartitionClause() != null) {
            LoadDataPartitionClauseContext loadDataPartitionClause = ctx.loadDataPartitionClause();
            int outerStartIndex = loadDataPartitionClause.PARTITION().getSymbol().getStartIndex();
            int outerStopIndex = loadDataPartitionClause.RP_().getSymbol().getStopIndex();
            result.setPartitionProperties(createCommonPartitionPropertiesSegment(loadDataPartitionClause.partitionSpec(), outerStartIndex, outerStopIndex));
        }

        return result;
    }

    @Override
    public ASTNode visitMerge(MergeContext ctx) {
        ImpalaMergeStatement result = new ImpalaMergeStatement();
        if (ctx.hint() != null) {
            result.getHints().add((HintSegment) visitHint(ctx.hint()));
        }

        //mergeIntoClause
        MergeIntoClauseContext intoClause = ctx.mergeIntoClause();
        if (intoClause.hint() != null) {
            result.getHints().add((HintSegment) visitHint(intoClause.hint()));
        }
        AliasSegment intoAlias = null;
        if (intoClause.alias() != null) {
            intoAlias = (AliasSegment) visitAlias(intoClause.alias());
        }
        if (intoClause.tableName() != null) {
            SimpleTableSegment table = (SimpleTableSegment) visitTableName(intoClause.tableName());
            table.setAlias(intoAlias);
            result.setTarget(table);
        } else if (intoClause.viewName() != null) {
            SimpleTableSegment view = (SimpleTableSegment) visitViewName(intoClause.viewName());
            view.setAlias(intoAlias);
            result.setTarget(view);
        } else if (intoClause.select() != null) {
            ImpalaSelectStatement selectStatement = (ImpalaSelectStatement) visitSelect(intoClause.select());
            SubqueryTableSegment subqueryTable = new SubqueryTableSegment(intoClause.select().start.getStartIndex(), intoClause.select().stop.getStopIndex(),
                    new SubquerySegment(intoClause.select().start.getStartIndex(), intoClause.select().stop.getStopIndex(), selectStatement, getOriginalText(intoClause.select())));
            subqueryTable.setAlias(intoAlias);
            result.setTarget(subqueryTable);
        }

        //usingClause
        UsingClauseContext usingClause = ctx.usingClause();
        if (usingClause.hint() != null) {
            result.getHints().add((HintSegment) visitHint(usingClause.hint()));
        }
        AliasSegment usingAlias = null;
        if (usingClause.alias() != null) {
            usingAlias = (AliasSegment) visitAlias(usingClause.alias());
        }
        if (usingClause.tableName() != null) {
            SimpleTableSegment table = (SimpleTableSegment) visitTableName(usingClause.tableName());
            table.setAlias(usingAlias);
            result.setSource(table);
        } else if (usingClause.viewName() != null) {
            SimpleTableSegment view = (SimpleTableSegment) visitViewName(usingClause.viewName());
            view.setAlias(usingAlias);
            result.setSource(view);
        } else if (usingClause.select() != null) {
            ImpalaSelectStatement selectStatement = (ImpalaSelectStatement) visitSelect(usingClause.select());
            SubqueryTableSegment subqueryTable = new SubqueryTableSegment(usingClause.select().start.getStartIndex(), usingClause.select().stop.getStopIndex(),
                    new SubquerySegment(usingClause.select().start.getStartIndex(), usingClause.select().stop.getStopIndex(), selectStatement, getOriginalText(usingClause.select())));
            subqueryTable.setAlias(usingAlias);
            result.setSource(subqueryTable);
        }
        ExpressionWithParamsSegment expressionWithParams = new ExpressionWithParamsSegment(usingClause.expr().start.getStartIndex(), usingClause.expr().stop.getStopIndex(), (ExpressionSegment) visitExpr(usingClause.expr()));
        result.setExpression(expressionWithParams);

        //whenClauses
        WhenClausesContext whenClauses = ctx.whenClauses();
        if (CollectionUtils.isNotEmpty(whenClauses.whenMatchedThenClause())) {
            for (WhenMatchedThenClauseContext each : whenClauses.whenMatchedThenClause()) {
                MergeWhenAndThenSegment whenAndThenSegment = new MergeWhenAndThenSegment(each.start.getStartIndex(), each.stop.getStopIndex(), getOriginalText(each));
                if (each.expr() != null) {
                    whenAndThenSegment.setAndExpr((ExpressionSegment) visitExpr(each.expr()));
                }
                if (each.mergeUpdateOrDelete().UPDATE() != null) {
                    ImpalaUpdateStatement update = new ImpalaUpdateStatement();
                    update.setSetAssignment((SetAssignmentSegment) visitSetAssignmentsClause(each.mergeUpdateOrDelete().setAssignmentsClause()));
                    whenAndThenSegment.setUpdate(update);
                }
                result.getWhenAndThenSegments().add(whenAndThenSegment);
            }
        }
        if (whenClauses.whenNotMatchedClause() != null) {
            WhenNotMatchedClauseContext whenNotMatchedClause = whenClauses.whenNotMatchedClause();
            MergeWhenAndThenSegment whenAndThenSegment = new MergeWhenAndThenSegment(whenNotMatchedClause.start.getStartIndex(), whenNotMatchedClause.stop.getStopIndex(), getOriginalText(whenNotMatchedClause));
            if (whenNotMatchedClause.expr() != null) {
                whenAndThenSegment.setAndExpr((ExpressionSegment) visitExpr(whenNotMatchedClause.expr()));
            }
            ImpalaInsertStatement insert = new ImpalaInsertStatement();
            if (whenNotMatchedClause.columnParenthesesNames() != null) {
                Collection<ColumnSegment> columnSegments = new LinkedList<>();
                for (ColumnNameContext each : whenNotMatchedClause.columnParenthesesNames().columnNames().columnName()) {
                    columnSegments.add((ColumnSegment) visitColumnName(each));
                }
                insert.setInsertColumns(new InsertColumnsSegment(whenNotMatchedClause.columnParenthesesNames().start.getStartIndex(), whenNotMatchedClause.columnParenthesesNames().stop.getStopIndex(),
                        columnSegments));
            }
            for (ValuesRowContext each : whenNotMatchedClause.originValues().originValuesRowList().valuesRow()) {
                insert.getValues().add(visitValuesRow(each));
            }
            whenAndThenSegment.setInsert(insert);
            result.getWhenAndThenSegments().add(whenAndThenSegment);
        }

        return result;
    }

    @Override
    public ASTNode visitUpsert(UpsertContext ctx) {
        ImpalaUpsertStatement result = new ImpalaUpsertStatement();
        if (CollectionUtils.isNotEmpty(ctx.upsertHintClause())) {
            for (UpsertHintClauseContext each : ctx.upsertHintClause()) {
                result.getHints().add(visitUpsertHintClause(each));
            }
        }
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (ctx.columnNames() != null) {
            Collection<ColumnSegment> columnSegments = new LinkedList<>();
            for (ColumnNameContext each : ctx.columnNames().columnName()) {
                columnSegments.add((ColumnSegment) visitColumnName(each));
            }
            result.setInsertColumns(new InsertColumnsSegment(ctx.LP_().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex(), columnSegments));
        }
        if (ctx.select() != null) {
            ImpalaSelectStatement select = (ImpalaSelectStatement) visitSelect(ctx.select());
            result.setSelect(new SubquerySegment(ctx.select().start.getStartIndex(), ctx.select().stop.getStopIndex(), select, getOriginalText(ctx.select())));
        }
        if (ctx.upsertValuesClause() != null) {
            result.getValues().addAll(getInsertValuesSegments(ctx.upsertValuesClause().rowConstructorList()));
        }
        return result;
    }

    @Override
    public ASTNode visitValues(ValuesContext ctx) {
        ImpalaValuesStatement result = new ImpalaValuesStatement();
        if (ctx.originValues() != null) {
            for (ValuesRowContext each : ctx.originValues().originValuesRowList().valuesRow()) {
                result.getValues().add(visitValuesRow(each));
            }
            return result;
        }
        if (ctx.selectFromValues() != null) {
            SelectFromValuesSegment selectFromValuesSegment = new SelectFromValuesSegment();
            selectFromValuesSegment.setProjections((ProjectionsSegment) visitProjections(ctx.selectFromValues().projections()));
            for (ValuesRowContext each : ctx.selectFromValues().originValues().originValuesRowList().valuesRow()) {
                selectFromValuesSegment.getFromValues().add(visitValuesRow(each));
            }
            if (ctx.selectFromValues().alias() != null) {
                selectFromValuesSegment.setAliasSegment((AliasSegment) visitAlias(ctx.selectFromValues().alias()));
            }
            result.setSelectFromValuesSegment(selectFromValuesSegment);
        }
        return result;
    }

    @Override
    public ASTNode visitCopyTestcase(CopyTestcaseContext ctx) {
        ImpalaCopyTestcaseStatement result = new ImpalaCopyTestcaseStatement();
        if (ctx.copyTestcaseTo() != null) {
            ImpalaCopyTestcaseStatement.ToSegment toSegment = new ImpalaCopyTestcaseStatement.ToSegment();
            toSegment.setDirPath(ctx.copyTestcaseTo().dirPath().getText());
            toSegment.setSelect((ImpalaSelectStatement) visitSelect(ctx.copyTestcaseTo().select()));
            result.setSegment(toSegment);
            return result;
        }
        if (ctx.copyTestcaseFrom() != null) {
            ImpalaCopyTestcaseStatement.FromSegment fromSegment = new ImpalaCopyTestcaseStatement.FromSegment();
            fromSegment.setFilePath(ctx.copyTestcaseFrom().filePath().getText());
            result.setSegment(fromSegment);
        }
        return result;
    }

    private void calculateParameterCount(final Collection<ExprContext> exprContexts) {
        for (ExprContext each : exprContexts) {
            visitExpr(each);
        }
    }
}
