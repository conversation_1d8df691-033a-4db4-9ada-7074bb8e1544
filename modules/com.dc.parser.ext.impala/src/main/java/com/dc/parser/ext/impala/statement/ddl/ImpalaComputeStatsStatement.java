package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.model.segment.database.impala.TableSampleSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.ComputeStatsStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Setter
public final class ImpalaComputeStatsStatement extends ComputeStatsStatement implements ImpalaStatement {

    private SimpleTableSegment table;
    @Getter
    private List<ColumnSegment> columns = new LinkedList<>();
    @Getter
    private boolean incremental;

    private PartitionPropertiesSegment partitionProperties;

    private TableSampleSegment tableSample;


    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.ofNullable(partitionProperties);
    }

    public Optional<TableSampleSegment> getTableSample() {
        return Optional.ofNullable(tableSample);
    }

}
