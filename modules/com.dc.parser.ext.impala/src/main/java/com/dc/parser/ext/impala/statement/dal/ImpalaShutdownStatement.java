package com.dc.parser.ext.impala.statement.dal;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.statement.dal.ShutdownStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ImpalaShutdownStatement extends ShutdownStatement implements ImpalaStatement {

    private String hostName;

    private Integer portNumber;

    private ExpressionSegment deadline;
}
