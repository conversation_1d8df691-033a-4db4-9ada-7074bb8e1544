package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.model.segment.database.impala.LocationSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.ddl.CreateFunctionStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ImpalaCreateFunctionStatement extends CreateFunctionStatement implements ImpalaStatement {

    private boolean ifNotExists;

    private LocationSegment location;

    private IdentifierValue symbol;

    private boolean aggregate;


}
