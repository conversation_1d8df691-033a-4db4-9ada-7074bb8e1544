package com.dc.parser.ext.impala.visitor.statement.type;

import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementParser.*;
import com.dc.parser.ext.impala.statement.dal.*;
import com.dc.parser.ext.impala.statement.ddl.ImpalaDescribeStatement;
import com.dc.parser.ext.impala.visitor.statement.ImpalaStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DALStatementVisitor;
import com.dc.parser.model.segment.dal.*;
import com.dc.parser.model.segment.dcl.UserSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

public final class ImpalaDALStatementVisitor extends ImpalaStatementVisitor implements DALStatementVisitor {

    @Override
    public ASTNode visitExplain(ExplainContext ctx) {
        ImpalaExplainStatement result = new ImpalaExplainStatement();
        if (ctx.select() != null) {
            result.setSqlStatement((SQLStatement) new ImpalaDMLStatementVisitor().visitSelect(ctx.select()));
        } else if (ctx.ctas() != null) {
            CtasContext ctas = ctx.ctas();
            if (ctas.createTableAsSelect() != null) {
                result.setSqlStatement((SQLStatement) new ImpalaDDLStatementVisitor().visitCreateTableAsSelect(ctas.createTableAsSelect()));
            } else {
                result.setSqlStatement((SQLStatement) new ImpalaDDLStatementVisitor().visitCreateKuduTableAsSelect(ctas.createKuduTableAsSelect()));
            }
        }
        return result;
    }

    @Override
    public ASTNode visitSet(SetContext ctx) {
        ImpalaSetStatement result = new ImpalaSetStatement();
        if (ctx.onlySet() != null) {
            return result;
        } else if (ctx.setAll() != null) {
            result.setSetAll(true);
            return result;
        } else {
            return visitSetQueryOption(ctx.setQueryOption());
        }

    }

    @Override
    public ASTNode visitSetQueryOption(SetQueryOptionContext ctx) {
        ImpalaSetStatement result = new ImpalaSetStatement();
        VariableAssignSegment variableAssignSegment = new VariableAssignSegment();
        variableAssignSegment.setVariable(new VariableSegment(ctx.queryOption().start.getStartIndex(), ctx.queryOption().stop.getStopIndex(), getOriginalText(ctx.queryOption())));
        variableAssignSegment.setAssignValue(getOriginalText(ctx.queryOptionValue()));
        result.getVariableAssigns().add(variableAssignSegment);
        return result;
    }

    @Override
    public ASTNode visitShow(ShowContext ctx) {
        if (ctx.showDatabases() != null) {
            return visitShowDatabases(ctx.showDatabases());
        } else if (ctx.showTables() != null) {
            return visitShowTables(ctx.showTables());
        } else if (ctx.showFunctions() != null) {
            return visitShowFunctions(ctx.showFunctions());
        } else if (ctx.showCreate() != null) {
            return visitShowCreate(ctx.showCreate());
        } else if (ctx.showStats() != null) {
            return visitShowStats(ctx.showStats());
        } else if (ctx.showPartitions() != null) {
            return visitShowPartitions(ctx.showPartitions());
        } else if (ctx.showFiles() != null) {
            return visitShowFiles(ctx.showFiles());
        } else if (ctx.showRoles() != null) {
            return visitShowRoles(ctx.showRoles());
        } else {
            return visitShowGrant(ctx.showGrant());
        }
    }

    @Override
    public ASTNode visitShowDatabases(ShowDatabasesContext ctx) {
        ImpalaShowDatabasesStatement result = new ImpalaShowDatabasesStatement();
        if (ctx.showLikeClause() != null) {
            ShowLikeClauseContext showLikeClause = ctx.showLikeClause();
            StringLiteralsContext stringLiterals = showLikeClause.stringLiterals();
            int filterStart = showLikeClause.LIKE() == null ? showLikeClause.stringLiterals().start.getStartIndex() : showLikeClause.LIKE().getSymbol().getStartIndex();
            ShowFilterSegment showFilterSegment = new ShowFilterSegment(filterStart, showLikeClause.stringLiterals().stop.getStopIndex());

            showFilterSegment.setLike(new ShowLikeSegment(stringLiterals.start.getStartIndex(), stringLiterals.stop.getStopIndex(), getOriginalText(stringLiterals)));
            result.setFilter(showFilterSegment);
        }
        return result;
    }

    @Override
    public ASTNode visitShowTables(ShowTablesContext ctx) {
        ImpalaShowTablesStatement result = new ImpalaShowTablesStatement();
        if (ctx.IN() != null) {
            DatabaseNameContext databaseName = ctx.databaseName();
            result.setFromDatabase(new FromDatabaseSegment(databaseName.start.getStartIndex(), databaseName.stop.getStopIndex(), (DatabaseSegment) visitDatabaseName(databaseName)));
        }
        if (ctx.showLikeClause() != null) {
            result.setFilter(visitShowLikeClause(ctx.showLikeClause()));
        }
        return result;
    }

    @Override
    public ASTNode visitShowFunctions(ShowFunctionsContext ctx) {
        ImpalaShowFunctionsStatement result = new ImpalaShowFunctionsStatement();
        result.setAggregate(ctx.AGGREGATE() != null);
        result.setAnalytic(ctx.ANALYTIC() != null);
        if (ctx.IN() != null) {
            DatabaseNameContext databaseName = ctx.databaseName();
            FromDatabaseSegment fromDatabase = new FromDatabaseSegment(ctx.IN().getSymbol().getStartIndex(), databaseName.stop.getStopIndex(), (DatabaseSegment) visitDatabaseName(databaseName));
            result.setFromDatabase(fromDatabase);
        }
        if (ctx.showLikeClause() != null) {
            result.setFilter(visitShowLikeClause(ctx.showLikeClause()));
        }
        return result;
    }

    @Override
    public ASTNode visitShowCreate(ShowCreateContext ctx) {
        if (ctx.TABLE() != null) {
            ImpalaShowCreateTableStatement result = new ImpalaShowCreateTableStatement();
            result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
            return result;
        } else {
            ImpalaShowCreateViewStatement result = new ImpalaShowCreateViewStatement();
            result.setViewName(getOriginalText(ctx.viewName()));
            return result;
        }
    }

    @Override
    public ASTNode visitShowStats(ShowStatsContext ctx) {
        ImpalaShowStatsStatement result = new ImpalaShowStatsStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (ctx.TABLE() != null) {
            result.setShowTable(true);
        } else {
            result.setShowColumn(true);
        }
        return result;
    }


    @Override
    public ASTNode visitShowPartitions(ShowPartitionsContext ctx) {
        ImpalaShowPartitionsStatement result = new ImpalaShowPartitionsStatement();
        if (ctx.RANGE() != null) {
            result.setRange(true);
        }
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (ctx.generalPartitionClause() != null) {
            GeneralPartitionClauseContext clause = ctx.generalPartitionClause();
            result.setPartitionProperties(createCommonPartitionPropertiesSegment(clause.partitionSpec(), clause.start.getStartIndex(), clause.stop.getStopIndex()));
        }
        return result;
    }

    @Override
    public ASTNode visitShowFiles(ShowFilesContext ctx) {
        ImpalaShowFilesStatement result = new ImpalaShowFilesStatement();
        result.setInTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (ctx.generalPartitionClause() != null) {
            GeneralPartitionClauseContext clause = ctx.generalPartitionClause();
            result.setPartitionProperties(createCommonPartitionPropertiesSegment(clause.partitionSpec(), clause.start.getStartIndex(), clause.stop.getStopIndex()));
        }
        return result;
    }

    @Override
    public ASTNode visitShowRoles(ShowRolesContext ctx) {
        ImpalaShowRolesStatement result = new ImpalaShowRolesStatement();
        if (ctx.GROUP() != null) {
            result.setGroupName(getOriginalText(ctx.groupName()));
        }
        return result;
    }

    @Override
    public ASTNode visitShowGrant(ShowGrantContext ctx) {
        if (ctx.showGrantUser() != null) {
            return visitShowGrantUser(ctx.showGrantUser());
        } else if (ctx.showGrantRole() != null) {
            return visitShowGrantRole(ctx.showGrantRole());
        } else {
            return visitShowGrantGroup(ctx.showGrantGroup());
        }
    }

    @Override
    public ASTNode visitShowGrantUser(ShowGrantUserContext ctx) {
        ImpalaShowGrantStatement result = new ImpalaShowGrantStatement();
        ImpalaShowGrantStatement.ShowGrantUserSegment showGrantUserSegment = new ImpalaShowGrantStatement.ShowGrantUserSegment();
        UserSegment userSegment = new UserSegment();
        userSegment.setUser(getOriginalText(ctx.username()));
        showGrantUserSegment.setUser(userSegment);
        if (ctx.showOnClause() != null) {
            showGrantUserSegment.setShowOnSegment(visitShowOnClause(ctx.showOnClause()));
        }
        result.setShowGrantUserSegment(showGrantUserSegment);
        return result;
    }

    @Override
    public ASTNode visitShowGrantRole(ShowGrantRoleContext ctx) {
        ImpalaShowGrantStatement result = new ImpalaShowGrantStatement();
        ImpalaShowGrantStatement.ShowGrantRoleSegment showGrantRoleSegment = new ImpalaShowGrantStatement.ShowGrantRoleSegment();
        showGrantRoleSegment.setRole(getOriginalText(ctx.roleName()));
        if (ctx.showOnClause() != null) {
            showGrantRoleSegment.setShowOnSegment(visitShowOnClause(ctx.showOnClause()));
        }
        result.setShowGrantRoleSegment(showGrantRoleSegment);
        return result;
    }

    @Override
    public ASTNode visitShowGrantGroup(ShowGrantGroupContext ctx) {
        ImpalaShowGrantStatement result = new ImpalaShowGrantStatement();
        ImpalaShowGrantStatement.ShowGrantGroupSegment showGrantGroupSegment = new ImpalaShowGrantStatement.ShowGrantGroupSegment();
        showGrantGroupSegment.setGroup(getOriginalText(ctx.groupName()));
        if (ctx.showOnClause() != null) {
            showGrantGroupSegment.setShowOnSegment(visitShowOnClause(ctx.showOnClause()));
        }
        result.setShowGrantGroupSegment(showGrantGroupSegment);
        return result;
    }

    @Override
    public ASTNode visitShutdown(ShutdownContext ctx) {
        ImpalaShutdownStatement result = new ImpalaShutdownStatement();
        if (ctx.shutdownArguments() != null) {
            ShutdownArgumentsContext args = ctx.shutdownArguments();
            if (args.deadline() == null) {
                result.setHostName(getOriginalText(args.hostName()));
                if (args.portNumber() != null) {
                    result.setPortNumber(Integer.parseInt(getOriginalText(args.portNumber())));
                }
            } else if (args.hostName() == null) {
                result.setDeadline(visitConstantExpression(args.deadline().constantExpression()));
            } else {
                result.setHostName(getOriginalText(args.hostName()));
                if (args.portNumber() != null) {
                    result.setPortNumber(Integer.parseInt(getOriginalText(args.portNumber())));
                }
                result.setDeadline(visitConstantExpression(args.deadline().constantExpression()));
            }
        }
        return result;
    }

    @Override
    public ASTNode visitUse(UseContext ctx) {
        ImpalaUseStatement result = new ImpalaUseStatement();
        result.setDatabase(getOriginalText(ctx.databaseName()));
        return result;
    }
}
