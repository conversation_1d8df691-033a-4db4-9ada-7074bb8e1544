package com.dc.parser.ext.impala.visitor.statement;

import com.dc.parser.model.enums.HintSegmentType;
import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementBaseVisitor;
import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementParser.*;
import com.dc.parser.model.segment.database.impala.*;
import com.dc.parser.ext.impala.statement.dal.ImpalaShowGrantStatement;
import com.dc.parser.ext.impala.statement.dml.ImpalaSelectStatement;
import com.dc.parser.ext.impala.visitor.statement.type.ImpalaDMLStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.enums.ParameterMarkerType;
import com.dc.parser.model.segment.dal.ShowFilterSegment;
import com.dc.parser.model.segment.dal.ShowLikeSegment;
import com.dc.parser.model.segment.database.impala.RowFormatSegment;
import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.segment.dml.assignment.InsertValuesSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.*;
import com.dc.parser.model.segment.dml.expr.complex.CommonExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.ParameterMarkerExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.SimpleExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.item.ExpressionProjectionSegment;
import com.dc.parser.model.segment.generic.*;
import com.dc.parser.model.segment.generic.LocationSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.util.SQLUtils;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.model.value.literal.impl.*;
import com.dc.parser.model.value.parametermarker.ParameterMarkerValue;
import lombok.Getter;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.misc.Interval;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Getter
public abstract class ImpalaStatementVisitor extends ImpalaStatementBaseVisitor<ASTNode> {

    private final Collection<ParameterMarkerSegment> parameterMarkerSegments = new LinkedList<>();

    @Override
    public ASTNode visitParameterMarker(ParameterMarkerContext ctx) {
        return new ParameterMarkerValue(parameterMarkerSegments.size(), ParameterMarkerType.QUESTION);
    }

    @Override
    public ASTNode visitLiterals(LiteralsContext ctx) {
        if (null != ctx.stringLiterals()) {
            return visitStringLiterals(ctx.stringLiterals());
        }
        if (null != ctx.numberLiterals()) {
            return visitNumberLiterals(ctx.numberLiterals());
        }
        if (null != ctx.temporalLiterals()) {
            return visitTemporalLiterals(ctx.temporalLiterals());
        }
        if (null != ctx.hexadecimalLiterals()) {
            return visitHexadecimalLiterals(ctx.hexadecimalLiterals());
        }
        if (null != ctx.bitValueLiterals()) {
            return visitBitValueLiterals(ctx.bitValueLiterals());
        }
        if (null != ctx.booleanLiterals()) {
            return visitBooleanLiterals(ctx.booleanLiterals());
        }
        if (null != ctx.nullValueLiterals()) {
            return visitNullValueLiterals(ctx.nullValueLiterals());
        }
        throw new IllegalStateException("Literals must have string, number, dateTime, hex, bit, boolean or null.");
    }

    @Override
    public ASTNode visitStringLiterals(StringLiteralsContext ctx) {
        return new StringLiteralValue(ctx.getText());
    }
    @Override
    public ASTNode visitNumberLiterals(NumberLiteralsContext ctx) {
        return new NumberLiteralValue(ctx.getText());
    }
    @Override
    public ASTNode visitTemporalLiterals(TemporalLiteralsContext ctx) {
        return new OtherLiteralValue(ctx.getText());
    }
    @Override
    public ASTNode visitHexadecimalLiterals(HexadecimalLiteralsContext ctx) {
        return new OtherLiteralValue(ctx.getText());
    }
    @Override
    public ASTNode visitBitValueLiterals(BitValueLiteralsContext ctx) {
        return new OtherLiteralValue(ctx.getText());
    }
    @Override
    public ASTNode visitBooleanLiterals(BooleanLiteralsContext ctx) {
        return new BooleanLiteralValue(ctx.getText());
    }
    @Override
    public ASTNode visitNullValueLiterals(NullValueLiteralsContext ctx) {
        return new NullLiteralValue(ctx.getText());
    }

    @Override
    public ASTNode visitExpr(ExprContext ctx) {
        if (ctx.booleanPrimary() != null) {
            return visitBooleanPrimary(ctx.booleanPrimary());
        } else if (ctx.andOperator() != null) {
            return createBinaryOperationExpression(ctx, ctx.andOperator().getText());
        } else if (ctx.orOperator() != null) {
            return createBinaryOperationExpression(ctx, ctx.orOperator().getText());
        } else if (ctx.XOR() != null) {
            return createBinaryOperationExpression(ctx, "XOR");
        } else if (ctx.notOperator() != null) {
            return new NotExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), (ExpressionSegment) visit(ctx.expr(0)), false);
        } else if (ctx.LP_() != null) {
            return visitExpr(ctx.expr(0));
        }

        return new NotExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), (ExpressionSegment) visit(ctx.expr(0)), false);
    }

    protected BinaryOperationExpression createBinaryOperationExpression(final ExprContext ctx, final String operator) {
        ExpressionSegment left = (ExpressionSegment) visitExpr(ctx.expr(0));
        ExpressionSegment right = (ExpressionSegment) visitExpr(ctx.expr(1));
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
    }

    @Override
    public ASTNode visitBooleanPrimary(BooleanPrimaryContext ctx) {
        if (ctx.IS() != null) {
            String rightText = "";
            if (ctx.NOT() != null) {
                rightText = rightText.concat(ctx.getStart().getInputStream().getText(new Interval(ctx.NOT().getSymbol().getStartIndex(), ctx.NOT().getSymbol().getStopIndex())))
                        .concat(" ");
            }
            Token operatorToken = null;
            if (ctx.TRUE() != null) {
                operatorToken = ctx.TRUE().getSymbol();
            } else if (ctx.FALSE() != null) {
                operatorToken = ctx.FALSE().getSymbol();
            } else if (ctx.UNKNOWN() != null) {
                operatorToken = ctx.UNKNOWN().getSymbol();
            } else if (ctx.NULL() != null) {
                operatorToken = ctx.NULL().getSymbol();
            }

            int startIndex = (operatorToken == null ? ctx.IS().getSymbol().getStopIndex() + 2 : operatorToken.getStartIndex());
            rightText = rightText.concat(ctx.start.getInputStream().getText(new Interval(startIndex, ctx.stop.getStopIndex())));
            ExpressionSegment right = new LiteralExpressionSegment(ctx.IS().getSymbol().getStopIndex() + 2, ctx.stop.getStopIndex(), rightText);
            String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
            ExpressionSegment left = (ExpressionSegment) visitBooleanPrimary(ctx.booleanPrimary());
            String operator = "IS";

            return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
        } else if (ctx.SAFE_EQ_() != null || ctx.comparisonOperator() != null) {
            return createCompareSegment(ctx);
        } else if (ctx.MEMBER() != null) {
            int startIndex = ctx.MEMBER().getSymbol().getStopIndex() + 5;
            int endIndex = ctx.stop.getStopIndex() - 1;
            String rightText = ctx.start.getInputStream().getText(new Interval(startIndex, endIndex));
            ExpressionSegment right = new ExpressionProjectionSegment(startIndex, endIndex, rightText);
            String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
            ExpressionSegment left = (ExpressionSegment) visitBooleanPrimary(ctx.booleanPrimary());
            String operator = "MEMBER OF";
            return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
        } else if (ctx.assignmentOperator() != null) {
            return createAssignmentSegment(ctx);
        } else {
            return visitPredicate(ctx.predicate());
        }
    }

    private ASTNode createCompareSegment(BooleanPrimaryContext ctx) {
        ExpressionSegment left = (ExpressionSegment) visitBooleanPrimary(ctx.booleanPrimary());
        ExpressionSegment right;
        String operator;
        if (ctx.ALL() != null) {
            operator = (ctx.SAFE_EQ_() == null ? ctx.comparisonOperator().getText() + " ALL" : ctx.SAFE_EQ_().getText());
        } else {
            operator = (ctx.SAFE_EQ_() == null ? ctx.comparisonOperator().getText() : ctx.SAFE_EQ_().getText());
        }
        if (ctx.predicate() != null) {
            right = (ExpressionSegment) visitPredicate(ctx.predicate());
        } else {
            right = new SubqueryExpressionSegment(new SubquerySegment(ctx.subquery().start.getStartIndex(), ctx.subquery().stop.getStopIndex(), (ImpalaSelectStatement) visitSubquery(ctx.subquery()),
                    getOriginalText(ctx.subquery())));
        }
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
    }

    private ASTNode createAssignmentSegment(BooleanPrimaryContext ctx) {
        ExpressionSegment left = (ExpressionSegment) visitBooleanPrimary(ctx.booleanPrimary());
        ExpressionSegment right = (ExpressionSegment) visitPredicate(ctx.predicate());
        String operator = ctx.assignmentOperator().getText();
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
    }

    @Override
    public ASTNode visitPredicate(PredicateContext ctx) {
        if (ctx.IN() != null) {
            return createInSegment(ctx);
        }
        if (ctx.BETWEEN() != null) {
            return createBetweenSegment(ctx);
        }
        if (ctx.LIKE() != null) {
            return createBinaryOperationExpressionFromLike(ctx);
        }
        if (ctx.REGEXP() != null) {
            return createBinaryOperationExpressionFromRegexp(ctx);
        }
        if (ctx.RLIKE() != null) {
            return createBinaryOperationExpressionFromRlike(ctx);
        }
        return visitBitExpr(ctx.bitExpr(0));
    }

    private InExpression createInSegment(PredicateContext ctx) {
        boolean not = (ctx.NOT() != null);
        ExpressionSegment left = (ExpressionSegment) visitBitExpr(ctx.bitExpr(0));
        ExpressionSegment right;
        if (ctx.subquery() != null) {
            right = new SubqueryExpressionSegment(new SubquerySegment(ctx.subquery().start.getStartIndex(), ctx.subquery().stop.getStopIndex(), (ImpalaSelectStatement) visitSubquery(ctx.subquery()),
                    getOriginalText(ctx.subquery())));
        } else {
            right = new ListExpression(ctx.LP_().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex());
            for (ExprContext exprContext : ctx.expr()) {
                ((ListExpression) right).getItems().add((ExpressionSegment) visitExpr(exprContext));
            }
        }
        return new InExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, not);
    }

    private BetweenExpression createBetweenSegment(PredicateContext ctx) {
        ExpressionSegment left = (ExpressionSegment) visitBitExpr(ctx.bitExpr(0));
        ExpressionSegment between = (ExpressionSegment) visitBitExpr(ctx.bitExpr(1));
        ExpressionSegment and = (ExpressionSegment) visitPredicate(ctx.predicate());
        boolean not = (null != ctx.NOT());
        return new BetweenExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, between, and, not);
    }

    private BinaryOperationExpression createBinaryOperationExpressionFromLike(PredicateContext ctx) {
        ExpressionSegment left = (ExpressionSegment) visitBitExpr(ctx.bitExpr(0));
        String operator;
        ExpressionSegment right;
        if (null != ctx.SOUNDS()) {
            right = (ExpressionSegment) visitBitExpr(ctx.bitExpr(1));
            operator = "SOUNDS LIKE";
        } else {
            ListExpression listExpression = new ListExpression(ctx.simpleExpr(0).start.getStartIndex(), ctx.simpleExpr().get(ctx.simpleExpr().size() - 1).stop.getStopIndex());
            for (SimpleExprContext simpleExprContext : ctx.simpleExpr()) {
                listExpression.getItems().add((ExpressionSegment) visit(simpleExprContext));
            }
            right = listExpression;
            operator = null == ctx.NOT() ? "LIKE" : "NOT LIKE";
        }
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
    }

    private BinaryOperationExpression createBinaryOperationExpressionFromRegexp(PredicateContext ctx) {
        ExpressionSegment left = (ExpressionSegment) visitBitExpr(ctx.bitExpr(0));
        ExpressionSegment right = (ExpressionSegment) visitBitExpr(ctx.bitExpr(1));
        String operator = null == ctx.NOT() ? "REGEXP" : "NOT REGEXP";
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
    }

    private BinaryOperationExpression createBinaryOperationExpressionFromRlike(PredicateContext ctx) {
        ExpressionSegment left = (ExpressionSegment) visitBitExpr(ctx.bitExpr(0));
        ExpressionSegment right = (ExpressionSegment) visitBitExpr(ctx.bitExpr(1));
        String operator = null == ctx.NOT() ? "RLIKE" : "NOT RLIKE";
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
    }


    @Override
    public ASTNode visitBitExpr(BitExprContext ctx) {
        if (null != ctx.simpleExpr()) {
            return visitSimpleExpr(ctx.simpleExpr());
        }
        ExpressionSegment left = (ExpressionSegment) visit(ctx.getChild(0));
        ExpressionSegment right;
        if (ctx.intervalExpression() != null) {
            right = (ExpressionSegment) visitIntervalExpression(ctx.intervalExpression());
        } else {
            right = (ExpressionSegment) visit(ctx.getChild(2));
        }
        String operator = ctx.getChild(1).getText();
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, operator, text);
    }

    @Override
    public ASTNode visitSimpleExpr(SimpleExprContext ctx) {
        int startIndex = ctx.start.getStartIndex();
        int stopIndex = ctx.stop.getStopIndex();
        if (null != ctx.subquery()) {
            SubquerySegment subquerySegment = new SubquerySegment(
                    ctx.subquery().getStart().getStartIndex(), ctx.subquery().getStop().getStopIndex(), (ImpalaSelectStatement) visit(ctx.subquery()), getOriginalText(ctx.subquery()));
            return null == ctx.EXISTS() ? new SubqueryExpressionSegment(subquerySegment) : new ExistsSubqueryExpression(startIndex, stopIndex, subquerySegment);
        }
        if (null != ctx.parameterMarker()) {
            ParameterMarkerValue parameterMarker = (ParameterMarkerValue) visitParameterMarker(ctx.parameterMarker());
            ParameterMarkerExpressionSegment result = new ParameterMarkerExpressionSegment(startIndex, stopIndex, parameterMarker.getValue(), parameterMarker.getType());
            parameterMarkerSegments.add(result);
            return result;
        }
        if (null != ctx.literals()) {
            return SQLUtils.createLiteralExpression(visitLiterals(ctx.literals()), startIndex, stopIndex, ctx.literals().start.getInputStream().getText(new Interval(startIndex, stopIndex)));
        }
        if (null != ctx.intervalExpression()) {
            return visitIntervalExpression(ctx.intervalExpression());
        }
        if (null != ctx.functionCall()) {
            return visitFunctionCall(ctx.functionCall());
        }
        if (null != ctx.collateClause()) {
            ExpressionSegment expr = null == ctx.simpleExpr() ? null : (ExpressionSegment) visitSimpleExpr(ctx.simpleExpr(0));
            return new CollateExpression(startIndex, stopIndex, (SimpleExpressionSegment) visitCollateClause(ctx.collateClause()), expr);
        }
        if (null != ctx.columnName()) {
            return visitColumnName(ctx.columnName());
        }
        if (null != ctx.matchExpression()) {
            return visitMatchExpression(ctx.matchExpression());
        }
        if (null != ctx.notOperator()) {
            ASTNode expression = visitSimpleExpr(ctx.simpleExpr(0));
            if (expression instanceof ExistsSubqueryExpression) {
                ((ExistsSubqueryExpression) expression).setNot(true);
                return expression;
            }
            return new NotExpression(startIndex, stopIndex, (ExpressionSegment) expression, "!".equalsIgnoreCase(ctx.notOperator().getText()));
        }
        if (null != ctx.LP_() && 1 == ctx.expr().size()) {
            return visitExpr(ctx.expr(0));
        }
        if (null != ctx.OR_()) {
            ExpressionSegment left = (ExpressionSegment) visitSimpleExpr(ctx.simpleExpr(0));
            ExpressionSegment right = (ExpressionSegment) visitSimpleExpr(ctx.simpleExpr(1));
            String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
            return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), left, right, ctx.OR_().getText(), text);
        }
        return visitRemainSimpleExpr(ctx);
    }

    private ASTNode visitRemainSimpleExpr(final SimpleExprContext ctx) {
        if (null != ctx.caseExpression()) {
            return visitCaseExpression(ctx.caseExpression());
        }
        if (null != ctx.BINARY()) {
            return visitSimpleExpr(ctx.simpleExpr(0));
        }
        if (null != ctx.variable()) {
            return visitVariable(ctx.variable());
        }
        for (ExprContext each : ctx.expr()) {
            visitExpr(each);
        }
        for (SimpleExprContext each : ctx.simpleExpr()) {
            visitSimpleExpr(each);
        }
        String text = ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
        return new CommonExpressionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), text);
    }

    @Override
    public ASTNode visitColumnName(ColumnNameContext ctx) {
        ColumnSegment result = new ColumnSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IdentifierValue) visitIdentifier(ctx.identifier()));
        int nameCount = ctx.name().size();
        if (nameCount == 0) {
            return result;
        } else if (nameCount == 1) {
            NameContext realOwnerName = Optional.ofNullable(ctx.name(0)).orElse(ctx.name(1));
            OwnerSegment owner = new OwnerSegment(realOwnerName.start.getStartIndex(), realOwnerName.stop.getStopIndex(), (IdentifierValue) visitName(realOwnerName));
            result.setOwner(owner);
        } else if (nameCount == 2) {
            OwnerSegment directOwner = new OwnerSegment(ctx.name(1).start.getStartIndex(), ctx.name(1).stop.getStopIndex(), (IdentifierValue) visitName(ctx.name(1)));
            OwnerSegment biggestOwner = new OwnerSegment(ctx.name(0).start.getStartIndex(), ctx.name(0).stop.getStopIndex(), (IdentifierValue) visitName(ctx.name(0)));
            directOwner.setOwner(biggestOwner);
            result.setOwner(directOwner);
        }
        return result;
    }

    @Override
    public ASTNode visitIdentifier(IdentifierContext ctx) {
        return new IdentifierValue(ctx.getText());
    }

    @Override
    public ASTNode visitName(NameContext ctx) {
        if (ctx.identifier() != null) {
            return visitIdentifier(ctx.identifier());
        }
        return visit(ctx);
    }

    @Override
    public ASTNode visitSubquery(SubqueryContext ctx) {
        if (ctx.queryExpressionParens() != null) {
            return visitQueryExpressionParens(ctx.queryExpressionParens());
        }
        return visit(ctx);
    }

    @Override
    public final ASTNode visitTableName(final TableNameContext ctx) {
        SimpleTableSegment result = new SimpleTableSegment(new TableNameSegment(ctx.name().getStart().getStartIndex(),
                ctx.name().getStop().getStopIndex(), new IdentifierValue(ctx.name().identifier().getText())));
        OwnerContext owner = ctx.owner();
        if (null != owner) {
            result.setOwner((OwnerSegment) visitOwner(owner));
        }
        return result;
    }

    @Override
    public final ASTNode visitOwner(final OwnerContext ctx) {
        return new OwnerSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (IdentifierValue) visitIdentifier(ctx.identifier()));
    }

    //partition 公共方法
    protected PartitionPropertiesSegment createCommonPartitionPropertiesSegment(List<PartitionSpecContext> partitionSpecs, int outerStartIndex, int outerStopIndex) {
        PartitionPropertiesSegment result = new PartitionPropertiesSegment(outerStartIndex, outerStopIndex);
        Collection<ColumnSegment> allColumns = getAllPartitionColumns(partitionSpecs);

        result.getPartitionColumns().addAll(allColumns);
        return result;
    }

    private Collection<ColumnSegment> getAllPartitionColumns(List<PartitionSpecContext> partitionSpecs) {
        Collection<ColumnSegment> result = new LinkedList<>();

        for (PartitionSpecContext partitionSpec : partitionSpecs) {
            result.addAll(getColumnsByPartitionSpec(partitionSpec));
        }
        return result;
    }

    private Collection<ColumnSegment> getColumnsByPartitionSpec(PartitionSpecContext partitionSpec) {
        if (partitionSpec.simplePartitionSpec() != null) {
            NameContext name = partitionSpec.simplePartitionSpec().name();
            return List.of(new ColumnSegment(name.start.getStartIndex(), name.stop.getStopIndex(), new IdentifierValue(name.getText())));
        } else {
            return extractColumnsComplex(partitionSpec.complexPartitionSpec().comparisonExpressionOnPartitionCol());
        }
    }

    private Collection<ColumnSegment> extractColumnsComplex(ComparisonExpressionOnPartitionColContext ceopc) {
        Collection<ColumnSegment> result = new LinkedList<>();
        if (ceopc.comparisonOperator() != null || ceopc.BETWEEN() != null) {
            result.add(new ColumnSegment(ceopc.name().start.getStartIndex(), ceopc.name().stop.getStopIndex(), new IdentifierValue(ceopc.name().getText())));
            return result;
        } else if (ceopc.LP_() != null) {
            for (ComparisonExpressionOnPartitionColContext each : ceopc.comparisonExpressionOnPartitionCol()) {
                result.addAll(extractColumnsComplex(each));
            }
            return result;
        } else if (ceopc.logicOp != null) {
            result.addAll(extractColumnsComplex(ceopc.left));
            result.addAll(extractColumnsComplex(ceopc.right));
            return result;
        } else {
            NameContext name = ceopc.simplePartitionSpec().name();
            result.add(new ColumnSegment(name.start.getStartIndex(), name.stop.getStopIndex(), new IdentifierValue(name.getText())));
            return result;
        }
    }

    @Override
    public ASTNode visitDatabaseName(DatabaseNameContext ctx) {
        return new DatabaseSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), new IdentifierValue(ctx.getText()));
    }

    @Override
    public ASTNode visitFunctionName(FunctionNameContext ctx) {
        IdentifierContext identifier = ctx.identifier();
        if (ctx.owner() != null) {
            OwnerSegment ownerSegment = new OwnerSegment(ctx.owner().start.getStartIndex(), ctx.owner().stop.getStopIndex(), new IdentifierValue(ctx.owner().getText()));
            FunctionNameSegment functionNameSegment = new FunctionNameSegment(identifier.start.getStartIndex(), identifier.stop.getStopIndex(), new IdentifierValue(identifier.getText()));
            functionNameSegment.setOwner(ownerSegment);
            return functionNameSegment;
        } else {
            return new FunctionNameSegment(identifier.start.getStartIndex(), identifier.stop.getStopIndex(), new IdentifierValue(identifier.getText()));
        }
    }

    @Override
    public ASTNode visitLocationClause(LocationClauseContext ctx) {
        return new LocationSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.hdfsPath().getText());
    }

    @Override
    public ASTNode visitStoredAsClause(StoredAsClauseContext ctx) {
        StoredAsSegment result = new StoredAsSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        result.setFileFormat(new IdentifierValue(ctx.fileFormat().getText()));
        return result;
    }

    @Override
    public ASTNode visitStoredAsCTASClause(StoredAsCTASClauseContext ctx) {
        StoredAsSegment result = new StoredAsSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        result.setFileFormat(new IdentifierValue(ctx.ctasFileFormat().getText()));
        return result;
    }

    @Override
    public ASTNode visitRowFormatClause(RowFormatClauseContext ctx) {
        RowFormatSegment result = new RowFormatSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        if (ctx.FIELDS() != null) {
            result.setFieldsBy(new IdentifierValue(ctx.STRING_(0).getText()));
            if (ctx.LINES() != null && ctx.ESCAPED() != null) {
                result.setLinesBy(new IdentifierValue(ctx.STRING_(2).getText()));
            } else {
                result.setLinesBy(new IdentifierValue(ctx.STRING_(1).getText()));
            }
        } else if (ctx.LINES() != null){
            result.setLinesBy(new IdentifierValue(ctx.STRING_(0).getText()));
        }

        return result;
    }

    @Override
    public ASTNode visitCommentClause(CommentClauseContext ctx) {
        return new CommentSegment(ctx.STRING_().getText(), ctx.COMMENT().getSymbol().getStartIndex(), ctx.STRING_().getSymbol().getStopIndex());
    }

    @Override
    public ASTNode visitPartitionedByClause(PartitionedByClauseContext ctx) {
        PartitionPropertiesSegment result = new PartitionPropertiesSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());

        List<ColumnSegment> box = new LinkedList<>(); //中间产物。相当于盒子。临时承载。
        for (PartitionColumnContext partitionColumnContext : ctx.partitionColumn()) {
            box.add((ColumnSegment) visitColumnName(partitionColumnContext.columnName()));
        }

        result.getPartitionColumns().addAll(box);
        return result;
    }

    @Override
    public ASTNode visitPartitionByKuduClause(PartitionByKuduClauseContext ctx) {
        PartitionPropertiesSegment result = new PartitionPropertiesSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());

        if (ctx.hashClause() != null && !ctx.hashClause().isEmpty()) {
            for (HashClauseContext hashClause : ctx.hashClause()) {
                if (hashClause.pkCol() != null && !hashClause.pkCol().isEmpty()) {
                    result.getPartitionColumns().addAll(createColumnSegmentsByPkCols(hashClause.pkCol()));
                }
            }
        }
        return result;
    }

    public List<ColumnSegment> createColumnSegmentsByPkCols(List<PkColContext> pkCols) {
        List<ColumnSegment> columnSegments = new ArrayList<>();
        for (PkColContext pkCol : pkCols) {
            columnSegments.add(createOneColumnSegmentByPkCol(pkCol));
        }
        return columnSegments;
    }

    public ColumnSegment createOneColumnSegmentByPkCol(PkColContext pkCol) {
        return new ColumnSegment(pkCol.start.getStartIndex(), pkCol.stop.getStopIndex(), new IdentifierValue(pkCol.getText()));
    }

    public List<ColumnSegment> createColumnSegmentsByNames(List<ColumnNameContext> columnNames) {
        List<ColumnSegment> columnSegments = new ArrayList<>();
        for (ColumnNameContext columnName : columnNames) {
            columnSegments.add((ColumnSegment) visitColumnName(columnName));
        }
        return columnSegments;
    }

    public List<ImpalaConstraintDefinitionSegment> createConstraintDefinitions(ConstraintSpecificationContext ctx) {
        return Collections.emptyList();
    }

    @Override
    public ASTNode visitKuduColumnDefinition(KuduColumnDefinitionContext ctx) {
        ImpalaColumnDefinitionSegment result = new ImpalaColumnDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx));
        if (ctx.columnName() != null) {
            result.setColumnName((ColumnSegment) visitColumnName(ctx.columnName()));
            result.setDataType((DataTypeSegment) visitDataType(ctx.dataType()));
        }
        if (ctx.kuduColumnAttribute() != null && !ctx.kuduColumnAttribute().isEmpty()) {
            for (KuduColumnAttributeContext each : ctx.kuduColumnAttribute()) {
                if (each.NOT() != null && each.NULL() != null) {
                    result.setNotNull(true);
                }
                if (each.PRIMARY() != null && each.LP_() == null) {
                    result.setPrimaryKey(true);
                }
                if (each.DEFAULT() != null) {
                    result.setDefaultSegment(new DefaultSegment(each.constant().getText()));
                }
            }
        }
        if (ctx.commentColumnClause() != null) {
            result.setComment((CommentSegment) visitCommentColumnClause(ctx.commentColumnClause()));
        }

        return result;
    }

    @Override
    public ASTNode visitColumnDefinition(ColumnDefinitionContext ctx) {
        ImpalaColumnDefinitionSegment result = new ImpalaColumnDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx));
        if (ctx.columnName() != null) {
            result.setColumnName((ColumnSegment) visitColumnName(ctx.columnName()));
            result.setDataType((DataTypeSegment) visitDataType(ctx.dataType()));
        }
        if (ctx.constraintSpecification() != null) {
            ConstraintSpecificationContext constraint = ctx.constraintSpecification();
            if (constraint.NOT() != null && constraint.NULL() != null) {
                result.setNotNull(true);
            }
            if (constraint.PRIMARY() != null && constraint.LP_() == null) {
                result.setPrimaryKey(true);
            }
            if (constraint.DEFAULT() != null) {
                result.setDefaultSegment(new DefaultSegment(constraint.constant().getText()));
            }
        }
        if (ctx.commentColumnClause() != null) {
            result.setComment((CommentSegment) visitCommentColumnClause(ctx.commentColumnClause()));
        }

        return result;
    }

    @Override
    public ASTNode visitCommentColumnClause(CommentColumnClauseContext ctx) {
        return new CommentSegment(ctx.STRING_().getText(), ctx.COMMENT().getSymbol().getStartIndex(), ctx.STRING_().getSymbol().getStopIndex());
    }

    @Override
    public ASTNode visitViewName(ViewNameContext ctx) {
        SimpleTableSegment result = new SimpleTableSegment(new TableNameSegment(ctx.name().start.getStartIndex(),
                ctx.name().stop.getStopIndex(), new IdentifierValue(ctx.name().getText())));
        if (ctx.owner() != null) {
            result.setOwner((OwnerSegment) visitOwner(ctx.owner()));
        }
        return result;
    }

    @Override
    public ImpalaColumnDefinitionSegment visitAlterViewColumnDefinition(AlterViewColumnDefinitionContext ctx) {
        ImpalaColumnDefinitionSegment result = new ImpalaColumnDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx));
        result.setColumnName((ColumnSegment) visitColumnName(ctx.columnName()));
        if (ctx.commentClause() != null) {
            result.setComment((CommentSegment) visitCommentClause(ctx.commentClause()));
        }
        return result;
    }

    @Override
    public ImpalaColumnDefinitionSegment visitColSpec(ColSpecContext ctx) {
        ImpalaColumnDefinitionSegment result = new ImpalaColumnDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx));
        result.setColumnName((ColumnSegment) visitColumnName(ctx.columnName()));
        result.setDataType((DataTypeSegment) visitDataType(ctx.dataType()));
        if (ctx.commentClause() != null) {
            result.setComment((CommentSegment) visitCommentClause(ctx.commentClause()));
        }
        if (ctx.kuduAttributes() != null) {
            KuduAttributesContext kuduAttributes = ctx.kuduAttributes();
            if (kuduAttributes.NOT() != null && kuduAttributes.NULL() != null) {
                result.setNotNull(true);
            }
            if (kuduAttributes.DEFAULT() != null) {
                result.setDefaultSegment(new DefaultSegment(kuduAttributes.constant().getText()));
            }
        }
        return result;
    }

    @Override
    public InsertValuesSegment visitValuesRow(ValuesRowContext ctx) {
        List<ExpressionSegment> expressionSegments = ctx.valuesRowElement().stream().map(each -> (ExpressionSegment) visitValuesRowElement(each)).collect(Collectors.toList());
        return new InsertValuesSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), expressionSegments);
    }

    @Override
    public ASTNode visitValuesRowElement(ValuesRowElementContext ctx) {
        return visitBitExpr(ctx.bitExpr());
    }

    @Override
    public ShowFilterSegment visitShowLikeClause(ShowLikeClauseContext ctx) {
        StringLiteralsContext stringLiterals = ctx.stringLiterals();
        int filterStart = ctx.LIKE() == null ? ctx.stringLiterals().start.getStartIndex() : ctx.LIKE().getSymbol().getStartIndex();
        ShowFilterSegment showFilterSegment = new ShowFilterSegment(filterStart, ctx.stringLiterals().stop.getStopIndex());

        showFilterSegment.setLike(new ShowLikeSegment(stringLiterals.start.getStartIndex(), stringLiterals.stop.getStopIndex(), stringLiterals.getText()));
        return showFilterSegment;
    }

    @Override
    public ImpalaShowGrantStatement.ShowOnSegment visitShowOnClause(ShowOnClauseContext ctx) {
        ImpalaShowGrantStatement.ShowOnSegment showOnSegment = new ImpalaShowGrantStatement.ShowOnSegment();
        if (ctx.DATABASE() != null) {
            showOnSegment.setOnDatabase((DatabaseSegment) visitDatabaseName(ctx.databaseName()));
        } else if (ctx.TABLE() != null) {
            showOnSegment.setOnTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        } else if (ctx.URI() != null) {
            showOnSegment.setOnUri(ctx.uri().getText());
        } else {
            showOnSegment.setOnColumn((ColumnSegment) visitColumnName(ctx.columnName()));
        }
        return showOnSegment;
    }

    @Override
    public ExpressionSegment visitConstantExpression(ConstantExpressionContext ctx) {
        if (ctx.primaryExpression().size() == 1) {
            return visitPrimaryExpression(ctx.primaryExpression(0));
        }
        ExpressionSegment bigLeft = visitPrimaryExpression(ctx.primaryExpression(0));
        String bigOperator = ctx.binaryOperator(0).getText();
        List<PrimaryExpressionContext> primaryExpressions = ctx.primaryExpression().stream().skip(1).collect(Collectors.toList());
        List<BinaryOperatorContext> binaryOperators = ctx.binaryOperator().stream().skip(1).collect(Collectors.toList());
        ExpressionSegment bigRight = null;
        if (binaryOperators.isEmpty()) {
            bigRight = visitPrimaryExpression(primaryExpressions.get(0));
        } else {
            bigRight = createBigRightExpression(binaryOperators, primaryExpressions);
        }

        return new BinaryOperationExpression(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), bigLeft, bigRight, bigOperator, getOriginalText(ctx));
    }

    private ExpressionSegment createBigRightExpression(List<BinaryOperatorContext> binaryOperators, List<PrimaryExpressionContext> primaryExpressions) {

        if (binaryOperators.isEmpty() && primaryExpressions.size() == 1) {
            return new UnaryOperationExpression(primaryExpressions.get(0).start.getStartIndex(),
                                                primaryExpressions.get(0).stop.getStopIndex(),
                                                visitPrimaryExpression(primaryExpressions.get(0)),
                                                null,
                                                primaryExpressions.get(0).getText());
        }

        PrimaryExpressionContext currentLeftContext = primaryExpressions.get(0);
        String currentOperator = binaryOperators.get(0).getText();
        PrimaryExpressionContext currentRightContext = primaryExpressions.get(1);

        return new BinaryOperationExpression(currentLeftContext.start.getStartIndex(),
                currentRightContext.stop.getStopIndex(),
                visitPrimaryExpression(currentLeftContext),
                createBigRightExpression(binaryOperators.stream().skip(1).collect(Collectors.toList()), primaryExpressions.stream().skip(1).collect(Collectors.toList())),
                currentOperator,
                getOriginalText(currentLeftContext) + currentOperator + getOriginalText(currentRightContext));
    }

    @Override
    public ExpressionSegment visitPrimaryExpression(PrimaryExpressionContext ctx) {
        if (ctx.constant() != null) {
            return new CommonExpressionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), ctx.constant().getText());
        } else if (ctx.unaryExpression() != null) {
            UnaryExpressionContext unary = ctx.unaryExpression();
            return new UnaryOperationExpression(unary.start.getStartIndex(),
                                                unary.stop.getStopIndex(),
                                                visitPrimaryExpression(unary.primaryExpression()),
                                                unary.unaryOperator().getText(),
                                                getOriginalText(unary));
        } else if (ctx.functionCall() != null) {
            return (ExpressionSegment) new ImpalaDMLStatementVisitor().visitFunctionCall(ctx.functionCall());
        } else {
            return visitConstantExpression(ctx.constantExpression());
        }
    }

    @Override
    public HintSegment visitUpsertHintClause(UpsertHintClauseContext ctx) {
        HintSegment hintSegment = new HintSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), getOriginalText(ctx));
        hintSegment.setHintSegmentType(HintSegmentType.BRACKETS_HINT);
        return hintSegment;
    }

    protected Collection<InsertValuesSegment> getInsertValuesSegments(RowConstructorListContext ctx) {
        Collection<InsertValuesSegment> result = new LinkedList<>();
        List<AssignmentValuesContext> assignmentValuesContexts = ctx.assignmentValues();
        for (AssignmentValuesContext each : assignmentValuesContexts) {
            result.add((InsertValuesSegment) visitAssignmentValues(each));
        }
        return result;
    }

    @Override
    public ASTNode visitDataType(DataTypeContext ctx) {
        DataTypeSegment result = new DataTypeSegment();
        result.setDataTypeName(getDataTypeName(ctx));
        result.setStartIndex(ctx.start.getStartIndex());
        result.setStopIndex(ctx.stop.getStopIndex());

        DataTypeLengthSegment dataTypeLengthSegment1 = getDataTypeLengthSegmentByFieldLength(ctx);
        if (dataTypeLengthSegment1 != null) {
            result.setDataLength(dataTypeLengthSegment1);
        }
        DataTypeLengthSegment dataTypeLengthSegment2 = getDataTypeLengthSegmentByPrecision(ctx);
        if (dataTypeLengthSegment2 != null) {
            result.setDataLength(dataTypeLengthSegment2);
        }
        return result;
    }

    protected String getDataTypeName(DataTypeContext dataTypeContext) {
        if (dataTypeContext.simpleDataType() != null) {
            return dataTypeContext.simpleDataType().dtname.getText();
        } else {
            ComplexTypeContext complexTypeContext = dataTypeContext.complexType();
            if (complexTypeContext.structType() != null) {
                return complexTypeContext.structType().ctname.getText();
            } else if (complexTypeContext.arrayType() != null) {
                return complexTypeContext.arrayType().ctname.getText();
            } else {
                return complexTypeContext.mapType().ctname.getText();
            }
        }
    }

    protected DataTypeLengthSegment getDataTypeLengthSegmentByFieldLength(DataTypeContext dataTypeContext) {
        if (dataTypeContext.simpleDataType() != null) {
            SimpleDataTypeContext simpleDataTypeContext = dataTypeContext.simpleDataType();
            if (simpleDataTypeContext.CHAR() != null || simpleDataTypeContext.VARCHAR() != null) {
                if (simpleDataTypeContext.INTEGER_().size() == 1) {
                    DataTypeLengthSegment result = new DataTypeLengthSegment();
                    result.setStartIndex(simpleDataTypeContext.INTEGER_(0).getSymbol().getStartIndex());
                    result.setStartIndex(simpleDataTypeContext.INTEGER_(0).getSymbol().getStopIndex());
                    result.setPrecision(new BigDecimal(simpleDataTypeContext.INTEGER_(0).getText()).intValue());
                    return result;
                }
            }
        }
        return null;
    }

    protected DataTypeLengthSegment getDataTypeLengthSegmentByPrecision(DataTypeContext dataTypeContext) {
        if (dataTypeContext.simpleDataType() != null) {
            SimpleDataTypeContext simpleDataTypeContext = dataTypeContext.simpleDataType();
            if (simpleDataTypeContext.prec != null) {
                DataTypeLengthSegment result = new DataTypeLengthSegment();
                result.setStartIndex(simpleDataTypeContext.prec.getStartIndex());
                result.setStopIndex(simpleDataTypeContext.prec.getStopIndex());
                result.setPrecision(Integer.parseInt(simpleDataTypeContext.prec.getText()));
                if (simpleDataTypeContext.scale != null) {
                    result.setStopIndex(simpleDataTypeContext.scale.getStopIndex());
                    result.setScale(Integer.parseInt(simpleDataTypeContext.scale.getText()));
                }
                return result;
            }
        }
        return null;
    }

    protected int getStart(ParserRuleContext ctx) {
        return ctx.start.getStartIndex();
    }

    protected int getStop(ParserRuleContext ctx) {
        return ctx.stop.getStopIndex();
    }

    protected String getOriginalText(ParserRuleContext ctx) {
        return ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
    }
}
