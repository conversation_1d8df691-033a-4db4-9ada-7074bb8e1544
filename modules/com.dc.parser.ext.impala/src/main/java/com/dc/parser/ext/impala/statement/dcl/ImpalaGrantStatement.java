package com.dc.parser.ext.impala.statement.dcl;

import com.dc.parser.model.segment.database.impala.GrantOnSegment;
import com.dc.parser.model.segment.database.impala.GrantToSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.dcl.RoleOrPrivilegeSegment;
import com.dc.parser.model.statement.dcl.GrantStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

@Setter
@Getter
public class ImpalaGrantStatement extends GrantStatement implements ImpalaStatement {

    private final Collection<RoleOrPrivilegeSegment> roleOrPrivilege = new LinkedList<>();

    private GrantOnSegment grantOn;

    private GrantToSegment grantTo;

    private boolean withGrantOption;

}
