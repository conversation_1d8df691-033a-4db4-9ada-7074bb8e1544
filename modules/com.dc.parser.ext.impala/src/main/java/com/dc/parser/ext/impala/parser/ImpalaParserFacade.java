package com.dc.parser.ext.impala.parser;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.api.parser.SQLLexer;
import com.dc.parser.model.api.parser.SQLParser;
import com.dc.parser.model.spi.DialectSQLParserFacade;

public final class ImpalaParserFacade implements DialectSQLParserFacade {

    @Override
    public Class<? extends SQLLexer> getLexerClass() {
        return ImpalaLexer.class;
    }

    @Override
    public Class<? extends SQLParser> getParserClass() {
        return ImpalaParser.class;
    }

    @Override
    public DatabaseType.Constant getDatabaseType() {
        return DatabaseType.Constant.IMPALA;
    }
}
