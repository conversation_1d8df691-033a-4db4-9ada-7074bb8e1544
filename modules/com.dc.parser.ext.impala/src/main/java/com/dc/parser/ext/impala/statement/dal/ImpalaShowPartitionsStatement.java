package com.dc.parser.ext.impala.statement.dal;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dal.ShowPartitionsStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaShowPartitionsStatement extends ShowPartitionsStatement implements ImpalaStatement {

    @Getter
    private boolean isRange;

    private SimpleTableSegment table;

    private PartitionPropertiesSegment partitionProperties;


    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.ofNullable(partitionProperties);
    }

}
