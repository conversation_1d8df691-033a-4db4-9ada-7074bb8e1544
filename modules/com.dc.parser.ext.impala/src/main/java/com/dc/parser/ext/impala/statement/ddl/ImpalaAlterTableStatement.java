package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.database.impala.*;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Setter
@Getter
public class ImpalaAlterTableStatement extends AlterTableStatement implements ImpalaStatement {

    private final Collection<ReplaceColumnDefinitionSegment> replaceColumnDefinitions = new LinkedList<>();

    private final Collection<ChangeColumnDefinitionSegment> changeColumnDefinitions = new LinkedList<>();

    private final Collection<AlterColumnDefinitionSegment> alterColumnDefinitions = new LinkedList<>();

    private SetOwnerUserSegment setOwnerUser;

    private AddPartitionSegment addPartition;

    private DropPartitionSegment dropPartition;

    private AlterTableSetSegment alterTableSet;

    private AlterColumnStatsKeySegment alterColumnStatsKey;

    public Optional<SetOwnerUserSegment> getSetOwnerUserSegment() {
        return Optional.ofNullable(setOwnerUser);
    }

}
