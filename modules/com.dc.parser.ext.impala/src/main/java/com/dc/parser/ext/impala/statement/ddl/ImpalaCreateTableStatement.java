package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.CreateTableStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
@Getter
public class ImpalaCreateTableStatement extends CreateTableStatement implements ImpalaStatement {

    private boolean ifNotExists;

    private PartitionPropertiesSegment partitionProperties;

    private SimpleTableSegment likeTable;

    private IdentifierValue likeFilePath;


    @Override
    public boolean isIfNotExists() {
        return ifNotExists;
    }

    @Override
    public Optional<SimpleTableSegment> getLikeTable() {
        return Optional.ofNullable(likeTable);
    }
}
