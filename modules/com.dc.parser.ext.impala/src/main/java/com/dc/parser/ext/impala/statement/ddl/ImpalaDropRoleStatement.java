package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.dcl.DropRoleStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Setter;

import java.util.Optional;

@Setter
public class ImpalaDropRoleStatement extends DropRoleStatement implements ImpalaStatement {

    private IdentifierValue roleName;

    @Override
    public Optional<IdentifierValue> getRoleName() {
        return Optional.ofNullable(roleName);
    }
}
