package com.dc.parser.ext.impala.statement.dal;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.dal.FromDatabaseSegment;
import com.dc.parser.model.segment.dal.ShowFilterSegment;
import com.dc.parser.model.statement.dal.ShowFunctionsStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
public class ImpalaShowFunctionsStatement extends ShowFunctionsStatement implements ImpalaStatement {
    @Getter
    private boolean aggregate;
    @Getter
    private boolean analytic;

    private FromDatabaseSegment fromDatabase;

    private ShowFilterSegment filter;


    public Optional<FromDatabaseSegment> getFromDatabase() {
        return Optional.ofNullable(fromDatabase);
    }

    public Optional<ShowFilterSegment> getFilter() {
        return Optional.ofNullable(filter);
    }

}
