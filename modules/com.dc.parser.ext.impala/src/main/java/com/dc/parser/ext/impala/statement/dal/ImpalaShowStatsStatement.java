package com.dc.parser.ext.impala.statement.dal;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dal.ShowStatsStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaShowStatsStatement extends ShowStatsStatement implements ImpalaStatement {

    @Getter
    private boolean isShowTable;
    @Getter
    private boolean isShowColumn;

    private SimpleTableSegment table;


    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }

}
