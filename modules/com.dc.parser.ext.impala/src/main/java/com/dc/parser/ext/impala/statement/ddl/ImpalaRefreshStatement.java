package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.RefreshStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaRefreshStatement extends RefreshStatement implements ImpalaStatement {

    private SimpleTableSegment simpleTable;

    private PartitionPropertiesSegment partitionProperties;

    public Optional<SimpleTableSegment> getSimpleTable() {
        return Optional.ofNullable(simpleTable);
    }

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.ofNullable(partitionProperties);
    }
}
