package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.HintSegment;
import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.WithSegment;
import com.dc.parser.model.statement.dml.InsertStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaInsertStatement extends InsertStatement implements ImpalaStatement {

    private WithSegment with;

    private HintSegment hint;

    private PartitionPropertiesSegment partitionProperties;

    public Optional<WithSegment> getWith() {
        return Optional.ofNullable(with);
    }

    public Optional<HintSegment> getHint() {
        return Optional.ofNullable(hint);
    }

    public Optional<PartitionPropertiesSegment> getPartitionProperties() {
        return Optional.ofNullable(partitionProperties);
    }
}
