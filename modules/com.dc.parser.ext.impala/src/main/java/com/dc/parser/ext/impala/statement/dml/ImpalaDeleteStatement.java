package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.HintSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.JoinTableSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaDeleteStatement extends DeleteStatement implements ImpalaStatement {

    private HintSegment hint;

    private JoinTableSegment joinTable;

    public Optional<HintSegment> getHint() {
        return Optional.ofNullable(hint);
    }

    public Optional<JoinTableSegment> getJoinTable() {
        return Optional.ofNullable(joinTable);
    }
}
