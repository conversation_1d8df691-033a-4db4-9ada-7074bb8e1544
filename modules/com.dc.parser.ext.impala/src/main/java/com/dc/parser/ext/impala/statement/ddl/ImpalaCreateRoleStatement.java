package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.dcl.CreateRoleStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Setter;

import java.util.Optional;

@Setter
public class ImpalaCreateRoleStatement extends CreateRoleStatement implements ImpalaStatement {

    private IdentifierValue roleName;

    @Override
    public Optional<IdentifierValue> getRoleName() {
        return Optional.ofNullable(roleName);
    }
}
