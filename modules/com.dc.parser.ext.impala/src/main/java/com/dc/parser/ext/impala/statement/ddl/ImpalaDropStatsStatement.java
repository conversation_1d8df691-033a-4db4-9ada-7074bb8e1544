package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.model.segment.database.impala.PartitionPropertiesSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.DropStatisticsStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ImpalaDropStatsStatement extends DropStatisticsStatement implements ImpalaStatement {

    private SimpleTableSegment table;

    private boolean incremental;

    private PartitionPropertiesSegment partitionProperties;
}
