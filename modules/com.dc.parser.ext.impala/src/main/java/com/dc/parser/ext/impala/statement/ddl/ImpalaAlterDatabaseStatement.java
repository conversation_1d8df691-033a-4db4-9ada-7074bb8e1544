package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.ddl.AlterDatabaseStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ImpalaAlterDatabaseStatement extends AlterDatabaseStatement implements ImpalaStatement {

    private IdentifierValue ownerUsername;
}
