package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.model.segment.database.impala.ImpalaColumnDefinitionSegment;
import com.dc.parser.model.segment.database.impala.SetOwnerUserSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.ext.impala.statement.dml.ImpalaSelectStatement;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.ddl.AlterViewStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Setter
@Getter
public class ImpalaAlterViewStatement extends AlterViewStatement implements ImpalaStatement {

    private List<ImpalaColumnDefinitionSegment> columnDefinitions = new LinkedList<>();

    private SimpleTableSegment renameView;

    private SetOwnerUserSegment setOwnerUser;

    private ImpalaSelectStatement select;

    public Optional<SimpleTableSegment> getRenameView() {
        return Optional.ofNullable(renameView);
    }

    public Optional<SelectStatement> getSelect() {
        return Optional.ofNullable(select);
    }
}
