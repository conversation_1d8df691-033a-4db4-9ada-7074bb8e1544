package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.model.segment.database.impala.LocationSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.CommentSegment;
import com.dc.parser.model.statement.ddl.CreateDatabaseStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public class ImpalaCreateDatabaseStatement extends CreateDatabaseStatement implements ImpalaStatement {

    private CommentSegment comment;

    private LocationSegment location;

    public Optional<CommentSegment> getComment() {
        return Optional.ofNullable(comment);
    }

    public Optional<LocationSegment> getLocation() {
        return Optional.ofNullable(location);
    }
}
