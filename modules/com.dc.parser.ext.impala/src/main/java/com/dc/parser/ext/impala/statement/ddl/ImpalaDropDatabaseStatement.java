package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.ddl.DropDatabaseStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ImpalaDropDatabaseStatement extends DropDatabaseStatement implements ImpalaStatement {

    private boolean restrict;

    private boolean cascade;

    private KeywordType keywordType;


    public enum KeywordType {
        DATABASE, SCHEMA
    }

}
