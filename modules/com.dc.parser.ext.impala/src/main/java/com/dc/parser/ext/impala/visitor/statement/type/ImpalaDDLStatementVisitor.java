package com.dc.parser.ext.impala.visitor.statement.type;

import com.dc.parser.model.enums.FileFormatType;
import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementParser;
import com.dc.parser.ext.impala.parser.autogen.ImpalaStatementParser.*;
import com.dc.parser.ext.impala.statement.ddl.*;
import com.dc.parser.ext.impala.statement.dml.ImpalaSelectStatement;
import com.dc.parser.ext.impala.visitor.statement.ImpalaStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DDLStatementVisitor;
import com.dc.parser.model.segment.database.impala.*;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.DropColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.generic.CommentSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.segment.generic.DefaultSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.Token;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

public class ImpalaDDLStatementVisitor extends ImpalaStatementVisitor implements DDLStatementVisitor {

    @Override
    public ASTNode visitDescribe(DescribeContext ctx) {
        ImpalaDescribeStatement result = new ImpalaDescribeStatement();
        if (ctx.FORMATTED() != null) {
            result.setFormatted(true);
        }
        if (ctx.EXTENDED() != null) {
            result.setExtended(true);
        }

        if (ctx.DATABASE() != null) {
            result.setDatabase(true);
            if (ctx.objectName().databaseName() != null) {
                result.setObjectName(new IdentifierValue(getOriginalText(ctx.objectName().databaseName())));
            }
        }

        if (result.getObjectName() == null) {
            result.setObjectName(new IdentifierValue(getOriginalText(ctx.objectName())));
        }
        return result;
    }

    @Override
    public ASTNode visitRefresh(RefreshContext ctx) {
        ImpalaRefreshStatement result = new ImpalaRefreshStatement();

        //tableName
        result.setSimpleTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        //partition
        if (ctx.generalPartitionClause() != null) {
            ImpalaStatementParser.GeneralPartitionClauseContext generalPartitionClause = ctx.generalPartitionClause();
            result.setPartitionProperties(createCommonPartitionPropertiesSegment(generalPartitionClause.partitionSpec(),
                    generalPartitionClause.PARTITION().getSymbol().getStartIndex(), generalPartitionClause.RP_().getSymbol().getStopIndex()));
        }

        return result;
    }


    @Override
    public ASTNode visitRefreshFunctions(RefreshFunctionsContext ctx) {
        ImpalaRefreshFunctionsStatement result = new ImpalaRefreshFunctionsStatement();
        result.setDbName(new IdentifierValue(ctx.databaseName().getText()));
        return result;
    }

    @Override
    public ASTNode visitCreateDatabase(CreateDatabaseContext ctx) {
        ImpalaCreateDatabaseStatement result = new ImpalaCreateDatabaseStatement();
        //if not exists
        result.setIfNotExists(ctx.ifNotExists() != null);
        //databaseName
        result.setDatabaseName((DatabaseSegment) visitDatabaseName(ctx.databaseName()));
        //comment
        if (ctx.COMMENT() != null) {
            result.setComment(createCommentSegment(ctx.COMMENT().getSymbol(), ctx.STRING_().getText()));
        }
        //location
        if (ctx.LOCATION() != null) {
            result.setLocation(createLocationSegment(ctx.LOCATION().getSymbol(), ctx.hdfsPath().getText()));
        }

        return result;
    }

    @Override
    public ASTNode visitCreateFunction(CreateFunctionContext ctx) {
        if (ctx.createScalarCppUdf() != null) {
            return visitCreateScalarCppUdf(ctx.createScalarCppUdf());
        } else if (ctx.createScalarJavaUdf() != null) {
            return visitCreateScalarJavaUdf(ctx.createScalarJavaUdf());
        } else {
            return visitCreateAggregateUda(ctx.createAggregateUda());
        }
    }

    @Override
    public ASTNode visitCreateScalarCppUdf(CreateScalarCppUdfContext ctx) {
        ImpalaCreateFunctionStatement result = new ImpalaCreateFunctionStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        //functionName
        result.setFunctionName((FunctionNameSegment) visitFunctionName(ctx.functionName()));
        //location
        result.setLocation(createLocationSegment(ctx.LOCATION().getSymbol(), ctx.hdfsPathToDotSo().getText()));
        //symbol
        result.setSymbol(new IdentifierValue(ctx.STRING_().getText()));

        return result;
    }

    @Override
    public ASTNode visitCreateScalarJavaUdf(CreateScalarJavaUdfContext ctx) {
        ImpalaCreateFunctionStatement result = new ImpalaCreateFunctionStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setFunctionName((FunctionNameSegment) visitFunctionName(ctx.functionName()));
        result.setLocation(createLocationSegment(ctx.LOCATION().getSymbol(), ctx.hdfsPathToJar().getText()));
        result.setSymbol(new IdentifierValue(ctx.STRING_().getText()));

        return result;
    }

    @Override
    public ASTNode visitCreateAggregateUda(CreateAggregateUdaContext ctx) {
        ImpalaCreateFunctionStatement result = new ImpalaCreateFunctionStatement();
        result.setAggregate(ctx.AGGREGATE() != null);
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setFunctionName((FunctionNameSegment) visitFunctionName(ctx.functionName()));
        result.setLocation(createLocationSegment(ctx.LOCATION().getSymbol(), ctx.hdfsPath().getText()));

        return result;
    }

    @Override
    public ASTNode visitCreateRole(CreateRoleContext ctx) {
        ImpalaCreateRoleStatement result = new ImpalaCreateRoleStatement();
        result.setRoleName(new IdentifierValue(ctx.name().getText()));
        return result;
    }

    @Override
    public ASTNode visitCreateTable(CreateTableContext ctx) {

        if (ctx.createTableExplicitColumnDefinition() != null) {
            return visitCreateTableExplicitColumnDefinition(ctx.createTableExplicitColumnDefinition());
        }
        if (ctx.createTableAsSelect() != null) {
            return visitCreateTableAsSelect(ctx.createTableAsSelect());
        }
        if (ctx.createTableColumnDefinitionFromFile() != null) {
            return visitCreateTableColumnDefinitionFromFile(ctx.createTableColumnDefinitionFromFile());
        }
        if (ctx.createInternalKuduTable() != null) {
            return visitCreateInternalKuduTable(ctx.createInternalKuduTable());
        }
        if (ctx.createExternalKuduTable() != null) {
            return visitCreateExternalKuduTable(ctx.createExternalKuduTable());
        }
        if (ctx.createKuduTableAsSelect() != null) {
            return visitCreateKuduTableAsSelect(ctx.createKuduTableAsSelect());
        }
        //兜底。无特征的对象。
        return new ImpalaCreateTableStatement();
    }

    @Override
    public ASTNode visitCreateKuduTableAsSelect(CreateKuduTableAsSelectContext ctx) {
        ImpalaCreateTableStatement result = new ImpalaCreateTableStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (ctx.primaryKeyClause() != null) {
            RelationalTableSegment relationalTable = new RelationalTableSegment(ctx.primaryKeyClause().start.getStartIndex(), ctx.primaryKeyClause().stop.getStopIndex());
            relationalTable.getConstraintDefinitions().add(createOneConstraintDefinitionByPrimaryKeyClause(ctx.primaryKeyClause()));
            result.setRelationalTable(relationalTable);
        }
        if (ctx.partitionByKuduClause() != null) {
            result.setPartitionProperties((PartitionPropertiesSegment) visitPartitionByKuduClause(ctx.partitionByKuduClause()));
        }
        //other clauses
        ImpalaCreateTableOptionSegment option = new ImpalaCreateTableOptionSegment(0, 0);
        if (ctx.commentClause() != null) {
            option.setCommentSegment((CommentSegment) visitCommentClause(ctx.commentClause()));
        }
        StoredAsSegment storedAsSegment = new StoredAsSegment(ctx.STORED().getSymbol().getStartIndex(), ctx.KUDU().getSymbol().getStopIndex());
        storedAsSegment.setFileFormat(new IdentifierValue("KUDU"));
        option.setStoredAs(storedAsSegment);
        result.setCreateTableOption(option);
        //as select
        ImpalaDMLStatementVisitor dmlStatementVisitor = new ImpalaDMLStatementVisitor();
        result.setSelectStatement((SelectStatement) dmlStatementVisitor.visitSelect(ctx.select()));

        return result;
    }

    @Override
    public ASTNode visitCreateExternalKuduTable(CreateExternalKuduTableContext ctx) {
        ImpalaCreateTableStatement result = new ImpalaCreateTableStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (CollectionUtils.isNotEmpty(ctx.kuduColumnDefinition())) {
            result.setRelationalTable(createRelationalTableSegmentForKudu(ctx.kuduColumnDefinition(), ctx.LP_().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex()));
        }
        if (ctx.primaryKeyClause() != null) {
            result.getRelationalTable().ifPresent(relationalTable -> {
                relationalTable.getConstraintDefinitions().add(createOneConstraintDefinitionByPrimaryKeyClause(ctx.primaryKeyClause()));
            });
        }
        if (ctx.partitionByKuduClause() != null) {
            result.setPartitionProperties((PartitionPropertiesSegment) visitPartitionByKuduClause(ctx.partitionByKuduClause()));
        }
        //other clauses
        ImpalaCreateTableOptionSegment option = new ImpalaCreateTableOptionSegment(0, 0);
        if (ctx.commentClause() != null) {
            option.setCommentSegment((CommentSegment) visitCommentClause(ctx.commentClause()));
        }
        StoredAsSegment storedAsSegment = new StoredAsSegment(ctx.STORED().getSymbol().getStartIndex(), ctx.KUDU().getSymbol().getStopIndex());
        storedAsSegment.setFileFormat(new IdentifierValue("KUDU"));
        option.setStoredAs(storedAsSegment);

        result.setCreateTableOption(option);
        return result;
    }

    @Override
    public ASTNode visitCreateInternalKuduTable(CreateInternalKuduTableContext ctx) {
        ImpalaCreateTableStatement result = new ImpalaCreateTableStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        //kuduColumnDefinitions
        result.setRelationalTable(createRelationalTableSegmentForKudu(ctx.kuduColumnDefinition(), ctx.LP_().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex()));
        //primaryKeyClause
        if (ctx.primaryKeyClause() != null) {
            result.getRelationalTable().ifPresent(relationalTable -> {
                relationalTable.getConstraintDefinitions().add(createOneConstraintDefinitionByPrimaryKeyClause(ctx.primaryKeyClause()));
            });
        }
        //partitionByKuduClause
        if (ctx.partitionByKuduClause() != null) {
            result.setPartitionProperties((PartitionPropertiesSegment) visitPartitionByKuduClause(ctx.partitionByKuduClause()));
        }
        //other clauses
        ImpalaCreateTableOptionSegment option = new ImpalaCreateTableOptionSegment(0, 0);
        if (ctx.commentClause() != null) {
            option.setCommentSegment((CommentSegment) visitCommentClause(ctx.commentClause()));
        }
        StoredAsSegment storedAsSegment = new StoredAsSegment(ctx.STORED().getSymbol().getStartIndex(), ctx.KUDU().getSymbol().getStopIndex());
        storedAsSegment.setFileFormat(new IdentifierValue("KUDU"));
        option.setStoredAs(storedAsSegment);

        result.setCreateTableOption(option);

        return result;
    }

    @Override
    public ASTNode visitCreateTableColumnDefinitionFromFile(CreateTableColumnDefinitionFromFileContext ctx) {
        ImpalaCreateTableStatement result = new ImpalaCreateTableStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName(0)));
        //like
        if (ctx.tableName(1) != null) {
            result.setLikeTable((SimpleTableSegment) visitTableName(ctx.tableName(1)));
        } else if (ctx.hdfsPathOfFile() != null) {
            result.setLikeFilePath(new IdentifierValue(ctx.hdfsPathOfFile().getText()));
        }
        //partition
        if (ctx.partitionedByClause() != null) {
            result.setPartitionProperties((PartitionPropertiesSegment) visitPartitionedByClause(ctx.partitionedByClause()));
        }
        //other clauses
        ImpalaCreateTableOptionSegment option = handleCommonCreateTableClauses(ctx.sortByClause(), ctx.commentClause(), ctx.rowFormatClause(), ctx.storedAsClause(), ctx.locationClause(), 0, 0);
        result.setCreateTableOption(option);

        return result;
    }

    @Override
    public ASTNode visitCreateTableAsSelect(CreateTableAsSelectContext ctx) {
        ImpalaCreateTableStatement result = new ImpalaCreateTableStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        //partition
        if (ctx.partitionedByClause() != null) {
            result.setPartitionProperties((PartitionPropertiesSegment) visitPartitionedByClause(ctx.partitionedByClause()));
        }
        //other clauses
        ImpalaCreateTableOptionSegment option = handleCommonCreateTableClauses(ctx.sortByClause(), ctx.commentClause(), ctx.rowFormatClause(), ctx.storedAsCTASClause(), ctx.locationClause(),
                ctx.tableName().stop.getStartIndex() + 1, ctx.AS().getSymbol().getStartIndex() - 1);

        result.setCreateTableOption(option);
        //as select
        ImpalaDMLStatementVisitor dmlStatementVisitor = new ImpalaDMLStatementVisitor();
        result.setSelectStatement((SelectStatement) dmlStatementVisitor.visitSelect(ctx.select()));

        return result;
    }

    private ImpalaCreateTableOptionSegment handleCommonCreateTableClauses(SortByClauseContext sortByClause, CommentClauseContext commentClause, RowFormatClauseContext rowFormatClause,
                                                                          ParserRuleContext storedAsOrCTASClause, LocationClauseContext locationClause, int outerStart, int outerStop) {
        ImpalaCreateTableOptionSegment option = new ImpalaCreateTableOptionSegment(outerStart, outerStop);
        //sort by
        if (sortByClause != null) {
            SortBySegment sortBySegment = new SortBySegment(sortByClause.start.getStartIndex(), sortByClause.stop.getStopIndex());
            for (ColumnNameContext columnNameContext : sortByClause.columnName()) {
                sortBySegment.getColumns().add((ColumnSegment) visitColumnName(columnNameContext));
            }
            option.setSortBy(sortBySegment);
        }
        //comment
        if (commentClause != null) {
            option.setCommentSegment((CommentSegment) visitCommentClause(commentClause));
        }
        //row format
        if (rowFormatClause != null) {
            option.setRowFormat((RowFormatSegment) visitRowFormatClause(rowFormatClause));
        }
        //stored as 或者 stored as ctas
        if (storedAsOrCTASClause != null) {
            if (storedAsOrCTASClause instanceof StoredAsClauseContext) {
                option.setStoredAs((StoredAsSegment) visitStoredAsClause((StoredAsClauseContext) storedAsOrCTASClause));
            } else if (storedAsOrCTASClause instanceof StoredAsCTASClauseContext) {
                option.setStoredAs((StoredAsSegment) visitStoredAsCTASClause((StoredAsCTASClauseContext) storedAsOrCTASClause));
            }
        }
        //location
        if (locationClause != null) {
            option.setLocation((LocationSegment) visitLocationClause(locationClause));
        }
        return option;
    }


    @Override
    public ASTNode visitCreateTableExplicitColumnDefinition(CreateTableExplicitColumnDefinitionContext ctx) {
        ImpalaCreateTableStatement result = new ImpalaCreateTableStatement();

        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        //columnDefinitions
        result.setRelationalTable(createRelationalTableSegment(ctx.columnDefinition(), ctx.LP_().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex()));
        //partition
        if (ctx.partitionedByClause() != null) {
            result.setPartitionProperties((PartitionPropertiesSegment) visitPartitionedByClause(ctx.partitionedByClause()));
        }
        ImpalaCreateTableOptionSegment option = new ImpalaCreateTableOptionSegment(ctx.RP_().getSymbol().getStopIndex(), ctx.stop.getStopIndex());

        //sort by
        if (ctx.sortByClause() != null) {
            SortBySegment sortBySegment = new SortBySegment(ctx.sortByClause().start.getStartIndex(), ctx.sortByClause().stop.getStopIndex());
            for (ColumnNameContext columnNameContext : ctx.sortByClause().columnName()) {
                sortBySegment.getColumns().add((ColumnSegment) visitColumnName(columnNameContext));
            }
            option.setSortBy(sortBySegment);
        }

        //comment
        if (ctx.commentClause() != null) {
            option.setCommentSegment((CommentSegment) visitCommentClause(ctx.commentClause()));
        }
        //row format
        if (ctx.rowFormatClause() != null) {
            option.setRowFormat((RowFormatSegment) visitRowFormatClause(ctx.rowFormatClause()));
        }
        //stored as
        if (ctx.storedAsClause() != null) {
            option.setStoredAs((StoredAsSegment) visitStoredAsClause(ctx.storedAsClause()));
        }
        //location
        if (ctx.locationClause() != null) {
            option.setLocation((LocationSegment) visitLocationClause(ctx.locationClause()));
        }

        result.setCreateTableOption(option);

        return result;
    }

    protected RelationalTableSegment createRelationalTableSegment(List<ColumnDefinitionContext> columnDefinitions, int outerStart, int outerStop) {
        RelationalTableSegment relationalTableSegment = new RelationalTableSegment(outerStart, outerStop);
        for (ColumnDefinitionContext columnDefinition : columnDefinitions) {
            relationalTableSegment.getColumnDefinitions().add((ColumnDefinitionSegment) visitColumnDefinition(columnDefinition));

            if (columnDefinition.constraintSpecification() != null && columnDefinition.constraintSpecification().PRIMARY() != null
                    && columnDefinition.constraintSpecification().LP_() != null) {
                relationalTableSegment.getConstraintDefinitions().addAll(createConstraintDefinitions(columnDefinition.constraintSpecification()));
            }
        }

        return relationalTableSegment;

    }

    @Override
    public List<ImpalaConstraintDefinitionSegment> createConstraintDefinitions(ConstraintSpecificationContext ctx) {
        List<ImpalaConstraintDefinitionSegment> result = new ArrayList<>();
        if (ctx.PRIMARY() != null && ctx.LP_() != null) {
            ImpalaConstraintDefinitionSegment oneResult = new ImpalaConstraintDefinitionSegment(ctx.PRIMARY().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex());
            oneResult.setPrimaryKey(true);
            oneResult.getPrimaryKeyColumns().addAll(createColumnSegmentsByNames(ctx.columnName()));
            result.add(oneResult);
        }

        if (ctx.foreignKeySpecification() != null && !ctx.foreignKeySpecification().isEmpty()) {
            result.addAll(createConstraintDefinitionsByForeignKeySpecifications(ctx.foreignKeySpecification()));
        }

        return result;
    }

    public List<ImpalaConstraintDefinitionSegment> createConstraintDefinitionsByForeignKeySpecifications(List<ForeignKeySpecificationContext> foreignKeySpecifications) {
        List<ImpalaConstraintDefinitionSegment> result = new ArrayList<>();
        for (ForeignKeySpecificationContext foreignKeySpecification : foreignKeySpecifications) {
            ImpalaConstraintDefinitionSegment oneResult = new ImpalaConstraintDefinitionSegment(foreignKeySpecification.FOREIGN().getSymbol().getStartIndex(), foreignKeySpecification.RP_(0).getSymbol().getStopIndex());
            oneResult.setForeignKey(true);
            oneResult.setReferencedTable((SimpleTableSegment) visitTableName(foreignKeySpecification.tableName()));
            result.add(oneResult);
        }
        return result;
    }

    @Override
    public List<ColumnSegment> createColumnSegmentsByNames(List<ColumnNameContext> columnNames) {
        return super.createColumnSegmentsByNames(columnNames);
    }

    protected CommentSegment createCommentSegment(Token commentToken, String text) {
        return new CommentSegment(text, commentToken.getStartIndex(), commentToken.getStopIndex());
    }

    protected LocationSegment createLocationSegment(Token locationToken, String path) {
        return new LocationSegment(locationToken.getStartIndex(), locationToken.getStopIndex(), path);
    }

    public ImpalaConstraintDefinitionSegment createOneConstraintDefinitionByPrimaryKeyClause(PrimaryKeyClauseContext ctx) {
        ImpalaConstraintDefinitionSegment result = new ImpalaConstraintDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        result.setPrimaryKey(true);
        result.getPrimaryKeyColumns().addAll(createColumnSegmentsByNames(ctx.columnName()));
        return result;
    }

    public List<ImpalaConstraintDefinitionSegment> createConstraintDefinitionsForKudu(KuduColumnAttributeContext ctx) {
        List<ImpalaConstraintDefinitionSegment> result = new ArrayList<>();
        if (ctx.PRIMARY() != null && ctx.LP_() != null) {
            ImpalaConstraintDefinitionSegment oneResult = new ImpalaConstraintDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
            oneResult.setPrimaryKey(true);
            oneResult.getPrimaryKeyColumns().addAll(createColumnSegmentsByNames(ctx.columnName()));
            result.add(oneResult);
        }

        return result;
    }


    protected RelationalTableSegment createRelationalTableSegmentForKudu(List<KuduColumnDefinitionContext> kuduColumnDefinitions, int outerStart, int outerStop) {
        RelationalTableSegment relationalTableSegment = new RelationalTableSegment(outerStart, outerStop);

        for (KuduColumnDefinitionContext kuduColumnDefinition : kuduColumnDefinitions) {
            relationalTableSegment.getColumnDefinitions().add((ColumnDefinitionSegment) visitKuduColumnDefinition(kuduColumnDefinition));

            if (kuduColumnDefinition.kuduColumnAttribute() != null && !kuduColumnDefinition.kuduColumnAttribute().isEmpty()) {
                for (KuduColumnAttributeContext each : kuduColumnDefinition.kuduColumnAttribute()) {
                    if (each.PRIMARY() != null && each.LP_() != null) {
                        relationalTableSegment.getConstraintDefinitions().addAll(createConstraintDefinitionsForKudu(each));
                    }
                }
            }

        }

        return relationalTableSegment;
    }

    @Override
    public ASTNode visitCreateView(CreateViewContext ctx) {
        ImpalaCreateViewStatement result = new ImpalaCreateViewStatement();
        result.setIfNotExists(ctx.ifNotExists() != null);
        result.setView((SimpleTableSegment) visitViewName(ctx.viewName()));
        result.setViewDefinition(getOriginalText(ctx.select()));
        ImpalaDMLStatementVisitor dmlStatementVisitor = new ImpalaDMLStatementVisitor();
        result.setSelect((SelectStatement) dmlStatementVisitor.visitSelect(ctx.select()));

        return result;
    }

    @Override
    public ASTNode visitAlterDatabase(AlterDatabaseContext ctx) {
        ImpalaAlterDatabaseStatement result = new ImpalaAlterDatabaseStatement();
        result.setDatabaseName((DatabaseSegment) visitDatabaseName(ctx.databaseName()));
        result.setOwnerUsername(new IdentifierValue(ctx.username().getText()));
        return result;
    }

    @Override
    public ASTNode visitAlterTable(AlterTableContext ctx) {
        if (ctx.alterTableRename() != null) {
            return visitAlterTableRename(ctx.alterTableRename());
        }
        if (ctx.alterTableAddColumns() != null) {
            return visitAlterTableAddColumns(ctx.alterTableAddColumns());
        }
        if (ctx.alterTableReplaceColumns() != null) {
            return visitAlterTableReplaceColumns(ctx.alterTableReplaceColumns());
        }
        if (ctx.alterTableAddColumn() != null) {
            return visitAlterTableAddColumn(ctx.alterTableAddColumn());
        }
        if (ctx.alterTableDropColumn() != null) {
            return visitAlterTableDropColumn(ctx.alterTableDropColumn());
        }
        if (ctx.alterTableChange() != null) {
            return visitAlterTableChange(ctx.alterTableChange());
        }
        if (ctx.alterTableSetOwnerUser() != null) {
            return visitAlterTableSetOwnerUser(ctx.alterTableSetOwnerUser());
        }
        if (ctx.alterTableAlterSetOrDrop() != null) {
            return visitAlterTableAlterSetOrDrop(ctx.alterTableAlterSetOrDrop());
        }
        if (ctx.alterTableAlterSetComment() != null) {
            return visitAlterTableAlterSetComment(ctx.alterTableAlterSetComment());
        }
        if (ctx.alterTableAddPartition() != null) {
            return visitAlterTableAddPartition(ctx.alterTableAddPartition());
        }
        if (ctx.alterTableAddRangePartition() != null) {
            return visitAlterTableAddRangePartition(ctx.alterTableAddRangePartition());
        }
        if (ctx.alterTableDropPartition() != null) {
            return visitAlterTableDropPartition(ctx.alterTableDropPartition());
        }
        if (ctx.alterTableDropRangePartition() != null) {
            return visitAlterTableDropRangePartition(ctx.alterTableDropRangePartition());
        }
        if (ctx.alterTableRecoverPartitions() != null) {
            return visitAlterTableRecoverPartitions(ctx.alterTableRecoverPartitions());
        }
        if (ctx.alterTableSet() != null) {
            return visitAlterTableSet(ctx.alterTableSet());
        }
        if (ctx.alterTableColumnStatsKey() != null) {
            return visitAlterTableColumnStatsKey(ctx.alterTableColumnStatsKey());
        }
        if (ctx.alterTableSetCachedOrUncached() != null) {
            return visitAlterTableSetCachedOrUncached(ctx.alterTableSetCachedOrUncached());
        }
        //兜底。
        return new ImpalaAlterTableStatement();
    }

    @Override
    public ASTNode visitAlterTableRename(AlterTableRenameContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName(0)));
        result.setRenameTable((SimpleTableSegment) visitTableName(ctx.tableName(1)));
        return result;
    }

    @Override
    public ASTNode visitAlterTableAddColumns(AlterTableAddColumnsContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        Collection<AddColumnDefinitionSegment> target = result.getAddColumnDefinitions();

        target.add(createAddColumnDefinitionSegmentByColSpecs(ctx.colSpec(), ctx.ADD().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex()));

        return result;
    }

    public AddColumnDefinitionSegment createAddColumnDefinitionSegmentByColSpecs(List<ColSpecContext> colSpecs, int outerStart, int outerStop) {
        List<ColumnDefinitionSegment> columnDefinitions = new LinkedList<>();
        for (ColSpecContext colSpec : colSpecs) {
            columnDefinitions.add(visitColSpec(colSpec));
        }
        return new AddColumnDefinitionSegment(outerStart, outerStop, columnDefinitions);
    }

    @Override
    public ASTNode visitAlterTableReplaceColumns(AlterTableReplaceColumnsContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        Collection<ReplaceColumnDefinitionSegment> target = result.getReplaceColumnDefinitions();
        target.add(createReplaceColumnDefinitionSegmentByColSpecs(ctx.colSpec(), ctx.REPLACE().getSymbol().getStartIndex(), ctx.RP_().getSymbol().getStopIndex()));
        return result;
    }

    public ReplaceColumnDefinitionSegment createReplaceColumnDefinitionSegmentByColSpecs(List<ColSpecContext> colSpecs, int outerStart, int outerStop) {
        List<ColumnDefinitionSegment> columnDefinitions = new LinkedList<>();
        for (ColSpecContext colSpec : colSpecs) {
            columnDefinitions.add(visitColSpec(colSpec));
        }
        return new ReplaceColumnDefinitionSegment(outerStart, outerStop, columnDefinitions);
    }

    @Override
    public ASTNode visitAlterTableAddColumn(AlterTableAddColumnContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        Collection<AddColumnDefinitionSegment> target = result.getAddColumnDefinitions();

        target.add(createAddColumnDefinitionSegmentByColSpecs(List.of(ctx.colSpec()), ctx.ADD().getSymbol().getStartIndex(), ctx.colSpec().stop.getStopIndex()));
        return result;
    }

    @Override
    public ASTNode visitAlterTableDropColumn(AlterTableDropColumnContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        Collection<DropColumnDefinitionSegment> target = result.getDropColumnDefinitions();
        target.add(createDropColumnDefinitionSegmentByColumnName(ctx.columnName(), ctx.DROP().getSymbol().getStartIndex(), ctx.columnName().stop.getStopIndex()));
        return result;
    }

    public DropColumnDefinitionSegment createDropColumnDefinitionSegmentByColumnName(ColumnNameContext columnName, int outerStart, int outerStop) {
        List<ColumnSegment> columns = new LinkedList<>();
        columns.add((ColumnSegment) visitColumnName(columnName));
        return new DropColumnDefinitionSegment(outerStart, outerStop, columns);
    }

    @Override
    public ASTNode visitAlterTableChange(AlterTableChangeContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        Collection<ChangeColumnDefinitionSegment> target = result.getChangeColumnDefinitions();
        target.add(createChangeColumnDefinitionSegmentByColumnNameAndColSpec(ctx.columnName(), ctx.colSpec(), ctx.CHANGE().getSymbol().getStartIndex(), ctx.colSpec().stop.getStopIndex()));
        return result;
    }

    public ChangeColumnDefinitionSegment createChangeColumnDefinitionSegmentByColumnNameAndColSpec(ColumnNameContext columnName, ColSpecContext colSpec, int outerStart, int outerStop) {
        ImpalaColumnDefinitionSegment newColumnDefinition = visitColSpec(colSpec);
        ChangeColumnDefinitionSegment changeColumnDefinition = new ChangeColumnDefinitionSegment(outerStart, outerStop, newColumnDefinition);
        changeColumnDefinition.setPreviousColumn((ColumnSegment) visitColumnName(columnName));

        return changeColumnDefinition;
    }

    @Override
    public ASTNode visitAlterTableSetOwnerUser(AlterTableSetOwnerUserContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        SetOwnerUserSegment setOwnerUser = new SetOwnerUserSegment(ctx.SET().getSymbol().getStartIndex(), ctx.username().stop.getStopIndex());
        setOwnerUser.setOwnerUsername(ctx.username().getText());
        result.setSetOwnerUser(setOwnerUser);
        return result;
    }

    @Override
    public ASTNode visitAlterTableAlterSetOrDrop(AlterTableAlterSetOrDropContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        Collection<AlterColumnDefinitionSegment> target = result.getAlterColumnDefinitions();

        int start = ctx.ALTER(1).getSymbol().getStartIndex();
        AlterColumnDefinitionSegment alterColumnDefinitionSegment = new AlterColumnDefinitionSegment(start);
        if (ctx.SET() != null) {
            alterColumnDefinitionSegment.setStopIndex(ctx.kuduStorageAttrAndValue().stop.getStopIndex());
            AlterColumnSetSegment set = new AlterColumnSetSegment(ctx.SET().getSymbol().getStartIndex(), ctx.kuduStorageAttrAndValue().stop.getStopIndex());
            if (ctx.kuduStorageAttrAndValue().DEFAULT() != null) {
                set.setDefaultSegment(new DefaultSegment(Optional.ofNullable(ctx.kuduStorageAttrAndValue().constant()).map(ConstantContext::getText).orElse(null)));
            }
            alterColumnDefinitionSegment.setAlterColumnSetSegment(set);
        } else if (ctx.DROP() != null) {
            alterColumnDefinitionSegment.setStopIndex(ctx.DEFAULT().getSymbol().getStopIndex());
            AlterColumnDropSegment drop = new AlterColumnDropSegment(ctx.DROP().getSymbol().getStartIndex(), ctx.DEFAULT().getSymbol().getStopIndex());
            drop.setDefault(true);
            alterColumnDefinitionSegment.setAlterColumnDropSegment(drop);
        }

        //columnName
        alterColumnDefinitionSegment.setColumnSegment((ColumnSegment) visitColumnName(ctx.columnName()));

        target.add(alterColumnDefinitionSegment);
        return result;
    }

    @Override
    public ASTNode visitAlterTableAlterSetComment(AlterTableAlterSetCommentContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        Collection<AlterColumnDefinitionSegment> target = result.getAlterColumnDefinitions();
        AlterColumnDefinitionSegment alterColumnDefinitionSegment = new AlterColumnDefinitionSegment(ctx.ALTER(1).getSymbol().getStartIndex(), ctx.STRING_().getSymbol().getStopIndex());
        AlterColumnSetSegment set = new AlterColumnSetSegment(ctx.SET().getSymbol().getStartIndex(), ctx.STRING_().getSymbol().getStopIndex());
        set.setCommentSegment(new CommentSegment(ctx.STRING_().getText(), ctx.COMMENT().getSymbol().getStartIndex(), ctx.STRING_().getSymbol().getStopIndex()));
        alterColumnDefinitionSegment.setAlterColumnSetSegment(set);

        alterColumnDefinitionSegment.setColumnSegment((ColumnSegment) visitColumnName(ctx.columnName()));

        target.add(alterColumnDefinitionSegment);
        return result;
    }

    @Override
    public ASTNode visitAlterTableAddPartition(AlterTableAddPartitionContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        AddPartitionSegment addPartitionSegment = new AddPartitionSegment();
        addPartitionSegment.setStartIndex(ctx.ADD().getSymbol().getStartIndex());
        addPartitionSegment.setStopIndex(ctx.partitionSpec().stop.getStopIndex());
        addPartitionSegment.setIfNotExists(ctx.ifNotExists() != null);
        addPartitionSegment.setPartitionProperties(createCommonPartitionPropertiesSegment(List.of(ctx.partitionSpec()), ctx.PARTITION().getSymbol().getStartIndex(), ctx.partitionSpec().stop.getStopIndex()));
        if (ctx.location_spec() != null) {
            addPartitionSegment.setLocation(new LocationSegment(getStart(ctx.location_spec()), getStop(ctx.location_spec()), ctx.location_spec().hdfsPathOfDirectory().getText()));
        }
        result.setAddPartition(addPartitionSegment);
        return result;
    }

    @Override
    public ASTNode visitAlterTableAddRangePartition(AlterTableAddRangePartitionContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        AddPartitionSegment addPartitionSegment = new AddPartitionSegment();
        addPartitionSegment.setStartIndex(ctx.ADD().getSymbol().getStartIndex());
        addPartitionSegment.setStopIndex(getStop(ctx.kuduPartitionSpec()));
        addPartitionSegment.setIfNotExists(ctx.ifNotExists() != null);
        addPartitionSegment.setRange(true);
        KuduPartitionSpecContext kuduPartitionSpec = ctx.kuduPartitionSpec();
        if (CollectionUtils.isNotEmpty(kuduPartitionSpec.rangeOperator())) {
            addPartitionSegment.setLeftConstant(new IdentifierValue(kuduPartitionSpec.constant(0).getText()));
            addPartitionSegment.setRightConstant(new IdentifierValue(kuduPartitionSpec.constant(1).getText()));
        } else {
            addPartitionSegment.setEqConstant(new IdentifierValue(kuduPartitionSpec.constant(0).getText()));
        }

        result.setAddPartition(addPartitionSegment);
        return result;
    }

    @Override
    public ASTNode visitAlterTableDropPartition(AlterTableDropPartitionContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        DropPartitionSegment dropPartitionSegment = new DropPartitionSegment();
        dropPartitionSegment.setStartIndex(ctx.DROP().getSymbol().getStartIndex());
        dropPartitionSegment.setStopIndex(getStop(ctx.partitionSpec()));
        dropPartitionSegment.setIfExists(ctx.ifExists() != null);
        dropPartitionSegment.setPartitionProperties(createCommonPartitionPropertiesSegment(List.of(ctx.partitionSpec()), ctx.PARTITION().getSymbol().getStartIndex(), getStop(ctx.partitionSpec())));
        if (ctx.PURGE() != null) {
            dropPartitionSegment.setPurge(true);
            dropPartitionSegment.setStopIndex(ctx.PURGE().getSymbol().getStopIndex());
        }

        result.setDropPartition(dropPartitionSegment);
        return result;
    }

    @Override
    public ASTNode visitAlterTableDropRangePartition(AlterTableDropRangePartitionContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        DropPartitionSegment dropPartitionSegment = new DropPartitionSegment();
        dropPartitionSegment.setStartIndex(ctx.DROP().getSymbol().getStartIndex());
        dropPartitionSegment.setStopIndex(getStop(ctx.kuduPartitionSpec()));
        dropPartitionSegment.setIfExists(ctx.ifExists() != null);
        dropPartitionSegment.setRange(true);

        KuduPartitionSpecContext kuduPartitionSpec = ctx.kuduPartitionSpec();
        if (CollectionUtils.isNotEmpty(kuduPartitionSpec.rangeOperator())) {
            dropPartitionSegment.setLeftConstant(new IdentifierValue(kuduPartitionSpec.constant(0).getText()));
            dropPartitionSegment.setRightConstant(new IdentifierValue(kuduPartitionSpec.constant(1).getText()));
        } else {
            dropPartitionSegment.setEqConstant(new IdentifierValue(kuduPartitionSpec.constant(0).getText()));
        }
        result.setDropPartition(dropPartitionSegment);
        return result;
    }

    @Override
    public ASTNode visitAlterTableRecoverPartitions(AlterTableRecoverPartitionsContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        return result;
    }

    @Override
    public ASTNode visitAlterTableSet(AlterTableSetContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        AlterTableSetSegment alterTableSetSegment = new AlterTableSetSegment();
        if (ctx.PARTITION() != null) {
            alterTableSetSegment.setPartitionProperties(createCommonPartitionPropertiesSegment(List.of(ctx.partitionSpec()), ctx.PARTITION().getSymbol().getStartIndex(), getStop(ctx.partitionSpec())));
        }
        if (ctx.FILEFORMAT() != null) {
            alterTableSetSegment.setFileFormat(createFileFormatSegment(ctx.fileFormat(), ctx.FILEFORMAT().getSymbol().getStartIndex(), getStop(ctx.fileFormat())));
        }
        if (ctx.rowFormatClause() != null) {
            alterTableSetSegment.setRowFormat((RowFormatSegment) visitRowFormatClause(ctx.rowFormatClause()));
        }
        if (ctx.locationClause() != null) {
            alterTableSetSegment.setLocation((LocationSegment) visitLocationClause(ctx.locationClause()));
        }
        result.setAlterTableSet(alterTableSetSegment);
        return result;
    }

    private FileFormatSegment createFileFormatSegment(FileFormatContext context, int outerStart, int outerStop) {
        FileFormatSegment fileFormatSegment = new FileFormatSegment();
        fileFormatSegment.setStartIndex(outerStart);
        fileFormatSegment.setStopIndex(outerStop);
        if (context.PARQUET() != null) {
            fileFormatSegment.setFileFormatType(FileFormatType.PARQUET);
        } else if (context.TEXTFILE() != null) {
            fileFormatSegment.setFileFormatType(FileFormatType.TEXTFILE);
        } else if (context.RCFILE() != null) {
            fileFormatSegment.setFileFormatType(FileFormatType.RCFILE);
        } else if (context.SEQUENCEFILE() != null) {
            fileFormatSegment.setFileFormatType(FileFormatType.SEQUENCEFILE);
        } else if (context.AVRO() != null) {
            fileFormatSegment.setFileFormatType(FileFormatType.AVRO);
        } else {
            fileFormatSegment.setFileFormatType(FileFormatType.UNKNOWN);
        }
        return fileFormatSegment;
    }

    @Override
    public ASTNode visitAlterTableColumnStatsKey(AlterTableColumnStatsKeyContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        AlterColumnStatsKeySegment alterColumnStatsKeySegment = new AlterColumnStatsKeySegment();
        alterColumnStatsKeySegment.setStartIndex(getStart(ctx.columnName()));
        alterColumnStatsKeySegment.setStopIndex(ctx.RP_().getSymbol().getStopIndex());
        if (ctx.SET() != null) {
            alterColumnStatsKeySegment.setStartIndex(ctx.SET().getSymbol().getStartIndex());
        }
        alterColumnStatsKeySegment.setColumn((ColumnSegment) visitColumnName(ctx.columnName()));
        List<KeyNameContext> keyNames = ctx.kvProperties().keyName();
        List<ValueNameContext> valueNames = ctx.kvProperties().valueName();
        Map<String, String> target = alterColumnStatsKeySegment.getStatsKeyValue();
        for (int i = 0; i < keyNames.size(); i++) {
            target.put(keyNames.get(i).getText(), valueNames.get(i).getText());
        }
        result.setAlterColumnStatsKey(alterColumnStatsKeySegment);
        return result;
    }

    @Override
    public ASTNode visitAlterTableSetCachedOrUncached(AlterTableSetCachedOrUncachedContext ctx) {
        ImpalaAlterTableStatement result = new ImpalaAlterTableStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));

        AlterTableSetCachedSegment setCachedSegment = new AlterTableSetCachedSegment();
        if (ctx.PARTITION() != null) {
            setCachedSegment.setPartitionProperties(createCommonPartitionPropertiesSegment(List.of(ctx.partitionSpec()), ctx.PARTITION().getSymbol().getStartIndex(), getStop(ctx.partitionSpec())));
        }
        if (ctx.CACHED() != null) {
            CachedSegment cachedSegment = new CachedSegment(ctx.CACHED().getSymbol().getStartIndex(), ctx.STRING_().getSymbol().getStopIndex());
            cachedSegment.setCachedType(CachedSegment.CachedType.CACHED);
            cachedSegment.setCachedIn(new IdentifierValue(ctx.STRING_().getText()));
            if (ctx.WITH() != null) {
                cachedSegment.setStopIndex(ctx.INTEGER_().getSymbol().getStopIndex());
                cachedSegment.setReplication(Integer.parseInt(ctx.INTEGER_().getText()));
            }
            setCachedSegment.setSetCachedSegment(cachedSegment);
        } else if (ctx.UNCACHED() != null) {
            CachedSegment cachedSegment = new CachedSegment(ctx.UNCACHED().getSymbol().getStartIndex(), ctx.UNCACHED().getSymbol().getStopIndex());
            cachedSegment.setCachedType(CachedSegment.CachedType.UNCACHED);
            setCachedSegment.setSetCachedSegment(cachedSegment);
        }

        result.setAlterTableSet(setCachedSegment);
        return result;
    }

    @Override
    public ASTNode visitAlterView(AlterViewContext ctx) {
        ImpalaAlterViewStatement result = new ImpalaAlterViewStatement();
        result.setView((SimpleTableSegment) visitViewName(ctx.viewName()));

        List<ImpalaColumnDefinitionSegment> target = result.getColumnDefinitions();

        if (CollectionUtils.isNotEmpty(ctx.alterViewColumnDefinition())) {
            for (AlterViewColumnDefinitionContext each : ctx.alterViewColumnDefinition()) {
                target.add(visitAlterViewColumnDefinition(each));
            }
        }

        AlterViewSuffixContext suffix = ctx.alterViewSuffix();
        if (suffix.alterViewSuffixAsSelect() != null) {
            result.setSelect((ImpalaSelectStatement) new ImpalaDMLStatementVisitor().visitSelect(suffix.alterViewSuffixAsSelect().select()));
            return result;
        }
        if (suffix.alterViewSuffixRenameTo() != null) {
            result.setRenameView((SimpleTableSegment) visitViewName(suffix.alterViewSuffixRenameTo().viewName()));
            return result;
        }
        if (suffix.alterViewSuffixSetOwnerUser() != null) {
            AlterViewSuffixSetOwnerUserContext ownerUserContext = suffix.alterViewSuffixSetOwnerUser();
            SetOwnerUserSegment setOwnerUserSegment = new SetOwnerUserSegment(ownerUserContext.SET().getSymbol().getStartIndex(), ownerUserContext.name().stop.getStopIndex());
            setOwnerUserSegment.setOwnerUsername(ownerUserContext.name().getText());
            result.setSetOwnerUser(setOwnerUserSegment);
            return result;
        }

        return result;
    }

    @Override
    public ASTNode visitComment(CommentContext ctx) {
        ImpalaCommentStatement result = new ImpalaCommentStatement();
        if (ctx.DATABASE() != null) {
            result.setDatabase((DatabaseSegment) visitDatabaseName(ctx.databaseName()));
        } else if (ctx.TABLE() != null) {
            result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        } else if (ctx.COLUMN() != null) {
            result.setColumn((ColumnSegment) visitColumnName(ctx.columnName()));
        }
        CommentContentContext commentContentContext = ctx.commentContent();
        if (commentContentContext.STRING_() != null) {
            result.setComment(new IdentifierValue(commentContentContext.STRING_().getText()));
        } else if (commentContentContext.NULL() != null) {
            result.setComment(new IdentifierValue(commentContentContext.NULL().getText()));
        }
        return result;
    }

    @Override
    public ASTNode visitComputeStats(ComputeStatsContext ctx) {
        ImpalaComputeStatsStatement result = new ImpalaComputeStatsStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (ctx.columnNames() != null) {
            ColumnNamesContext columnNamesContext = ctx.columnNames();
            for (ColumnNameContext each : columnNamesContext.columnName()) {
                result.getColumns().add((ColumnSegment) visitColumnName(each));
            }
        }
        if (ctx.TABLESAMPLE() != null) {
            TableSampleSegment tableSampleSegment = new TableSampleSegment(ctx.TABLESAMPLE().getSymbol().getStartIndex(), ctx.RP_(0).getSymbol().getStopIndex());
            tableSampleSegment.setPercentage(Integer.parseInt(ctx.percentage().getText()));
            if (ctx.REPEATABLE() != null) {
                tableSampleSegment.setStopIndex(ctx.RP_(1).getSymbol().getStopIndex());
                tableSampleSegment.setSeed(Integer.parseInt(ctx.seed().getText()));
            }
            result.setTableSample(tableSampleSegment);
        }
        if (ctx.INCREMENTAL() != null) {
            result.setIncremental(true);
            if (ctx.PARTITION() != null) {
                result.setPartitionProperties(createCommonPartitionPropertiesSegment(List.of(ctx.partitionSpec()), ctx.PARTITION().getSymbol().getStartIndex(), getStop(ctx.partitionSpec())));
            }
        }
        return result;
    }

    @Override
    public ASTNode visitDropDatabase(DropDatabaseContext ctx) {
        ImpalaDropDatabaseStatement result = new ImpalaDropDatabaseStatement();
        if (ctx.DATABASE() != null) {
            result.setKeywordType(ImpalaDropDatabaseStatement.KeywordType.DATABASE);
        } else {
            result.setKeywordType(ImpalaDropDatabaseStatement.KeywordType.SCHEMA);
        }
        if (ctx.ifExists() != null) {
            result.setIfExists(true);
        }
        result.setDatabaseName((DatabaseSegment) visitDatabaseName(ctx.databaseName()));
        if (ctx.RESTRICT() != null) {
            result.setRestrict(true);
        }
        if (ctx.CASCADE() != null) {
            result.setCascade(true);
        }
        return result;
    }

    @Override
    public ASTNode visitDropFunction(DropFunctionContext ctx) {
        if (ctx.dropCppUdfsAndUdas() != null) {
            return visitDropCppUdfsAndUdas(ctx.dropCppUdfsAndUdas());
        } else {
            return visitDropJavaUdfs(ctx.dropJavaUdfs());
        }
    }

    @Override
    public ASTNode visitDropCppUdfsAndUdas(DropCppUdfsAndUdasContext ctx) {
        ImpalaDropFunctionStatement result = new ImpalaDropFunctionStatement();
        result.setAggregate(ctx.AGGREGATE() != null);
        result.setIfExists(ctx.ifExists() != null);
        result.setFunctionName((FunctionNameSegment) visitFunctionName(ctx.functionName()));
        if (CollectionUtils.isNotEmpty(ctx.dataType())) {
            for (DataTypeContext each : ctx.dataType()) {
                result.getDataTypes().add((DataTypeSegment) visitDataType(each));
            }
        }
        return result;
    }

    @Override
    public ASTNode visitDropJavaUdfs(DropJavaUdfsContext ctx) {
        ImpalaDropFunctionStatement result = new ImpalaDropFunctionStatement();
        result.setIfExists(ctx.ifExists() != null);
        result.setFunctionName((FunctionNameSegment) visitFunctionName(ctx.functionName()));
        return result;
    }

    @Override
    public ASTNode visitDropRole(DropRoleContext ctx) {
        ImpalaDropRoleStatement result = new ImpalaDropRoleStatement();
        result.setRoleName(new IdentifierValue(ctx.name().getText()));
        return result;
    }

    @Override
    public ASTNode visitDropStats(DropStatsContext ctx) {
        ImpalaDropStatsStatement result = new ImpalaDropStatsStatement();
        result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        if (ctx.INCREMENTAL() != null) {
            result.setIncremental(true);
            result.setPartitionProperties(createCommonPartitionPropertiesSegment(List.of(ctx.partitionSpec()), ctx.PARTITION().getSymbol().getStartIndex(), getStop(ctx.partitionSpec())));
        }
        return result;
    }

    @Override
    public ASTNode visitDropTable(DropTableContext ctx) {
        ImpalaDropTableStatement result = new ImpalaDropTableStatement();
        result.setIfExists(ctx.ifExists() != null);
        result.getTables().add((SimpleTableSegment) visitTableName(ctx.tableName()));
        result.setPurge(ctx.PURGE() != null);
        return result;
    }

    @Override
    public ASTNode visitDropView(DropViewContext ctx) {
        ImpalaDropViewStatement result = new ImpalaDropViewStatement();
        result.setIfExists(ctx.ifExists() != null);
        result.getViews().add((SimpleTableSegment) visitViewName(ctx.viewName()));
        return result;
    }

    @Override
    public ASTNode visitInvalidateMetadata(InvalidateMetadataContext ctx) {
        ImpalaInvalidateMetadataStatement result = new ImpalaInvalidateMetadataStatement();
        if (ctx.tableName() != null) {
            result.setTable((SimpleTableSegment) visitTableName(ctx.tableName()));
        }
        return result;
    }

    @Override
    public ASTNode visitTruncateTable(TruncateTableContext ctx) {
        ImpalaTruncateTableStatement result = new ImpalaTruncateTableStatement();
        if (ctx.ifExists() != null) {
            result.setIfExists(true);
        }
        result.getTables().add((SimpleTableSegment) visitTableName(ctx.tableName()));
        return result;
    }
}
