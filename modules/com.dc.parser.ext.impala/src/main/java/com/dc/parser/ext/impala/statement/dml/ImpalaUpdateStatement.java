package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.model.segment.database.impala.HintSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.dml.UpdateStatement;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaUpdateStatement extends UpdateStatement implements ImpalaStatement {

    private HintSegment hint;

    private TableSegment from;

    public Optional<HintSegment> getHint() {
        return Optional.ofNullable(hint);
    }

    public Optional<TableSegment> getFrom() {
        return Optional.ofNullable(from);
    }

}
