package com.dc.parser.ext.impala.statement.dcl;

import com.dc.parser.model.segment.database.impala.RevokeOnSegment;
import com.dc.parser.model.segment.database.impala.RevokeToSegment;
import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.dcl.RoleOrPrivilegeSegment;
import com.dc.parser.model.statement.dcl.RevokeStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.LinkedList;

@Setter
@Getter
public class ImpalaRevokeStatement extends RevokeStatement implements ImpalaStatement {

    private final Collection<RoleOrPrivilegeSegment> roleOrPrivilege = new LinkedList<>();

    private RevokeOnSegment revokeOn;

    private RevokeToSegment revokeTo;

    private boolean grantOptionFor;

}
