package com.dc.parser.ext.impala.statement.dml;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.statement.dml.CopyTestcaseStatement;
import lombok.Getter;
import lombok.Setter;

@Setter
public final class ImpalaCopyTestcaseStatement extends CopyTestcaseStatement implements ImpalaStatement {

    @Getter
    private SQLSegment segment;


    @Setter
    @Getter
    public static class ToSegment implements SQLSegment {
        private int startIndex;
        private int stopIndex;
        private String dirPath;
        private ImpalaSelectStatement select;
    }
    @Setter
    @Getter
    public static class FromSegment implements SQLSegment{
        private int startIndex;
        private int stopIndex;
        private String filePath;
    }
}
