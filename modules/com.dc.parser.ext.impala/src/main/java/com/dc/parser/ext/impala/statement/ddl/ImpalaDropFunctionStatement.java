package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.statement.ddl.DropFunctionStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

@Setter
@Getter
public class ImpalaDropFunctionStatement extends DropFunctionStatement implements ImpalaStatement {

    private boolean aggregate;

    private boolean ifExists;

    private List<DataTypeSegment> dataTypes = new LinkedList<>();
}
