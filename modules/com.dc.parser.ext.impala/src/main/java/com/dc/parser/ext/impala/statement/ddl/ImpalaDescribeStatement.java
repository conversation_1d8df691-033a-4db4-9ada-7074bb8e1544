package com.dc.parser.ext.impala.statement.ddl;

import com.dc.parser.ext.impala.statement.ImpalaStatement;
import com.dc.parser.model.statement.ddl.DescribeStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
public final class ImpalaDescribeStatement extends DescribeStatement implements ImpalaStatement {
    @Getter
    private boolean isDatabase;
    @Getter
    private boolean formatted;
    @Getter
    private boolean extended;

    private IdentifierValue objectName;

    public Optional<IdentifierValue> getObjectName() {
        return Optional.ofNullable(objectName);
    }

}
