package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

public abstract class RefreshFunctionsStatement extends AbstractSQLStatement implements DDLStatement {

    public Optional<IdentifierValue> getDbName() {
        return Optional.empty();
    }

    public void setDbName(IdentifierValue dbName) {
    }
}
