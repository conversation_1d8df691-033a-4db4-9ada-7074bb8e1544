package com.dc.parser.rewrite.generator;

import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.context.statement.ddl.TruncateStatementContext;
import com.dc.parser.model.context.statement.dml.DeleteStatementContext;
import com.dc.parser.model.context.statement.dml.InsertStatementContext;
import com.dc.parser.model.context.statement.dml.SelectStatementContext;
import com.dc.parser.model.context.statement.dml.UpdateStatementContext;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableSegment;
import com.dc.parser.model.statement.ddl.TruncateStatement;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Optional;

/**
 * DML影响行数预估SQL生成器
 * <p>
 * 它的目标是生成一条全新的、伴生的SQL，而不是修改原始的DML语句。
 * <p>
 * 支持的SQL类型：UPDATE、DELETE、INSERT INTO SELECT、SELECT INTO、TRUNCATE
 */
@Slf4j
public final class DMLPrecomputationSQLGenerator {

    /**
     * 生成DML影响行数预估的COUNT查询
     *
     * @param sqlStatementContext SQL语句上下文
     * @param originalSql         原始SQL字符串（用于提取WHERE子句文本）
     * @return 伴生的COUNT查询SQL，如果不适用则返回null
     */
    public static String generate(final SQLStatementContext sqlStatementContext, final String originalSql) {
        if (sqlStatementContext instanceof UpdateStatementContext) {
            return generateFromUpdate((UpdateStatementContext) sqlStatementContext, originalSql);
        } else if (sqlStatementContext instanceof DeleteStatementContext) {
            return generateFromDelete((DeleteStatementContext) sqlStatementContext, originalSql);
        } else if (sqlStatementContext instanceof InsertStatementContext) {
            return generateFromInsert((InsertStatementContext) sqlStatementContext, originalSql);
        } else if (sqlStatementContext instanceof SelectStatementContext) {
            return generateFromSelectInto((SelectStatementContext) sqlStatementContext, originalSql);
        } else if (sqlStatementContext instanceof TruncateStatementContext) {
            return generateFromTruncate((TruncateStatementContext) sqlStatementContext, originalSql);
        }
        return null;
    }

    /**
     * 从UPDATE语句生成COUNT查询
     */
    private static String generateFromUpdate(final UpdateStatementContext updateContext, final String originalSql) {
        UpdateStatement updateStatement = updateContext.getSqlStatement();
        return generateCountQuery(updateStatement.getTable(), updateStatement.getWhere(), originalSql);
    }

    /**
     * 从DELETE语句生成COUNT查询
     */
    private static String generateFromDelete(final DeleteStatementContext deleteContext, final String originalSql) {
        DeleteStatement deleteStatement = deleteContext.getSqlStatement();
        return generateCountQuery(deleteStatement.getTable(), deleteStatement.getWhere(), originalSql);
    }

    /**
     * 从INSERT语句生成COUNT查询
     */
    private static String generateFromInsert(final InsertStatementContext insertContext, final String originalSql) {
        InsertStatement insertStatement = insertContext.getSqlStatement();

        // 获取INSERT SELECT的SubquerySegment
        SubquerySegment insertSelectSegment = insertStatement.getInsertSelect().get();
        return generateCountFromSubquery(insertSelectSegment, originalSql);
    }

    /**
     * 从SELECT INTO语句生成COUNT查询
     */
    private static String generateFromSelectInto(final SelectStatementContext selectContext, final String originalSql) {
        SelectStatement selectStatement = selectContext.getSqlStatement();

        // 检查是否有INTO子句
        if (!selectStatement.getIntoSegment().isPresent()) {
            log.warn("Cannot generate count query for SELECT statement: No INTO clause found.");
            return null;
        }

        // 从FROM表生成COUNT查询，复用现有的WHERE条件
        return generateCountQuery(selectStatement.getFrom().orElse(null), selectStatement.getWhere(), originalSql);
    }

    /**
     * 从TRUNCATE语句生成COUNT查询
     */
    private static String generateFromTruncate(final TruncateStatementContext truncateContext, final String originalSql) {
        TruncateStatement truncateStatement = truncateContext.getSqlStatement();

        // 获取所有要TRUNCATE的表
        Collection<SimpleTableSegment> tables = truncateStatement.getTables();
        if (tables.isEmpty()) {
            log.warn("Cannot generate count query for TRUNCATE statement: No tables found.");
            return null;
        }

        return generateCountFromTruncateTables(tables);
    }

    /**
     * 从TRUNCATE表集合生成COUNT查询
     */
    private static String generateCountFromTruncateTables(final Collection<SimpleTableSegment> tables) {
        if (tables.size() == 1) {
            // 单表TRUNCATE情况
            SimpleTableSegment table = tables.iterator().next();
            String tableName = extractTableName(table);
            if (tableName == null) {
                return null;
            }
            return "SELECT COUNT(*) as COUNT FROM " + tableName;
        } else {
            // 多表TRUNCATE情况：计算所有表的行数总和
            StringBuilder countSql = new StringBuilder("SELECT (");
            boolean first = true;

            for (SimpleTableSegment table : tables) {
                String tableName = extractTableName(table);
                if (tableName == null) {
                    log.warn("Skipping table in TRUNCATE count query due to failed table name extraction.");
                    continue;
                }

                if (!first) {
                    countSql.append(" + ");
                }
                countSql.append("(SELECT COUNT(*) FROM ").append(tableName).append(")");
                first = false;
            }

            if (first) {
                // 所有表名都提取失败
                log.error("Cannot generate count query for TRUNCATE statement: All table name extractions failed.");
                return null;
            }

            countSql.append(") as COUNT");
            return countSql.toString();
        }
    }

    /**
     * 从子查询段生成COUNT查询
     */
    private static String generateCountFromSubquery(final SubquerySegment subquerySegment, final String originalSql) {
        try {
            // 从原始SQL中提取SELECT子查询部分
            String selectSubquery = originalSql.substring(subquerySegment.getStartIndex(), subquerySegment.getStopIndex() + 1);

            // 构建COUNT查询: SELECT COUNT(*) FROM (原SELECT语句) AS subquery
            return "SELECT COUNT(*) as COUNT FROM (" + selectSubquery + ") AS subquery";
        } catch (Exception e) {
            // 如果提取失败，返回null
            log.error("Failed to extract SELECT subquery from INSERT INTO ... SELECT statement due to index out of bounds or other error.", e);
            return null;
        }
    }

    /**
     * 通用的COUNT查询生成方法
     *
     * @param tableSegment 表段
     * @param whereSegment WHERE条件段
     * @param originalSql  原始SQL字符串
     * @return 生成的COUNT查询SQL，如果无法生成则返回null
     */
    private static String generateCountQuery(final TableSegment tableSegment, final Optional<WhereSegment> whereSegment, final String originalSql) {
        // 获取表名
        String tableName = extractTableName(tableSegment);
        if (tableName == null) {
            return null;
        }

        // 构建COUNT查询
        StringBuilder countSql = new StringBuilder("SELECT COUNT(*) as COUNT FROM ").append(tableName);

        // 添加WHERE条件（如果存在）
        if (whereSegment.isPresent()) {
            String whereClause = extractWhereClause(whereSegment.get(), originalSql);
            if (whereClause != null) {
                countSql.append(" ").append(whereClause);
            }
        }

        return countSql.toString();
    }

    /**
     * 提取表名
     */
    private static String extractTableName(final TableSegment tableSegment) {
        if (!(tableSegment instanceof SimpleTableSegment)) {
            log.warn("Cannot extract table name: Unsupported table segment type '{}'. Only SimpleTableSegment is supported.", tableSegment.getClass().getName());
            return null;
        }

        SimpleTableSegment simpleTable = (SimpleTableSegment) tableSegment;
        return simpleTable.getTableName().getIdentifier().getValueWithQuoteCharacters();
    }

    /**
     * 提取WHERE子句文本
     */
    private static String extractWhereClause(final WhereSegment whereSegment, final String originalSql) {
        try {
            return originalSql.substring(whereSegment.getStartIndex(), whereSegment.getStopIndex() + 1);
        } catch (Exception e) {
            // 如果提取失败，返回null
            log.error("Failed to extract WHERE clause from original SQL due to index out of bounds.", e);
            return null;
        }
    }

    /**
     * 检查是否可以为给定的SQL语句上下文生成COUNT查询
     */
    public static boolean canGenerate(final SQLStatementContext sqlStatementContext) {
        if (sqlStatementContext instanceof UpdateStatementContext || sqlStatementContext instanceof DeleteStatementContext) {
            return true;
        }

        // 检查INSERT语句是否为INSERT INTO SELECT类型
        if (sqlStatementContext instanceof InsertStatementContext) {
            InsertStatementContext insertContext = (InsertStatementContext) sqlStatementContext;
            return insertContext.getInsertSelectContext() != null;
        }

        // 检查SELECT INTO语句
        if (sqlStatementContext instanceof SelectStatementContext) {
            SelectStatementContext selectContext = (SelectStatementContext) sqlStatementContext;
            return selectContext.getSqlStatement().getIntoSegment().isPresent();
        }

        // 检查TRUNCATE语句
        return sqlStatementContext instanceof TruncateStatementContext;
    }
}
