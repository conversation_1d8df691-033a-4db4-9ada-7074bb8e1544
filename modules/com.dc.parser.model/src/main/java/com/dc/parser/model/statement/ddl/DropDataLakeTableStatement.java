package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

public abstract class DropDataLakeTableStatement extends AbstractSQLStatement implements DDLStatement {

    public Optional<SimpleTableSegment> getTableName() {
        return Optional.empty();
    }
}
