package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.dml.statistics.StatisticsStrategySegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.dal.DALStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Update statistics statement.
 */
@Getter
@Setter
public abstract class UpdateStatisticsStatement extends AbstractSQLStatement implements DALStatement {

    private List<IndexSegment> indexes;

    private SimpleTableSegment table;

    private StatisticsStrategySegment strategy;
}
