package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.NameSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Create external table statement.
 */
@Getter
@Setter
public abstract class CreateExternalTableStatement extends AbstractSQLStatement implements DDLStatement {

    private NameSegment fileName;

    private SimpleTableSegment table;

    private SelectStatement selectStatement;
}
