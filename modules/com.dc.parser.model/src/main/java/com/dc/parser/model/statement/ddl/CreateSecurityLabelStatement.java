package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.ObjectNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

public abstract class CreateSecurityLabelStatement extends AbstractSQLStatement implements DDLStatement {

    public Optional<ObjectNameSegment> getSecurityLabelName() {
        return Optional.empty();
    }
}
