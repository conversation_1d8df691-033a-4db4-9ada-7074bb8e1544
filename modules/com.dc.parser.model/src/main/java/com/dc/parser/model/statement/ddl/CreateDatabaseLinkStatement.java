package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.dblink.DBLinkSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Create database link statement.
 */
@Getter
@Setter
@RequiredArgsConstructor
public abstract class CreateDatabaseLinkStatement extends AbstractSQLStatement implements DDLStatement {

    private final DBLinkSegment dbLinkSegment;
}
