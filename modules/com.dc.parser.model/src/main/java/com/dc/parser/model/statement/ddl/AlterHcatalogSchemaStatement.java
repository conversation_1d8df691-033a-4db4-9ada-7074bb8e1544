package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
@Getter
public abstract class AlterHcatalogSchemaStatement extends AbstractSQLStatement implements DDLStatement {
    private DatabaseSegment schemaName;

    /**
     * Get schemaName.
     *
     * @return schemaName
     */
    public Optional<DatabaseSegment> getSchemaName() {
        return Optional.ofNullable(schemaName);
    }

}
