package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

@Setter
@Getter
public abstract class CreateAccessPolicyStatement extends AbstractSQLStatement implements DDLStatement {
    private SimpleTableSegment table;

    /**
     * Get table.
     *
     * @return schemaName
     */
    public Optional<SimpleTableSegment> getTable() {
        return Optional.ofNullable(table);
    }
}

