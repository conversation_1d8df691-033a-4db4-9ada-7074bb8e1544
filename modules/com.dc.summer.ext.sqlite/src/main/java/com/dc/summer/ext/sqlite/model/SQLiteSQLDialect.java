
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.GenericSQLDialect;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.impl.sql.BasicSQLDialect;

public class SQLiteSQLDialect extends GenericSQLDialect {

    public SQLiteSQLDialect() {
        super("SQLite", "sqlite");
    }

    public void initDriverSettings(JDBCSession session, JDBCDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super.initDriverSettings(session, dataSource, metaData);
    }

    @Override
    public boolean supportsAliasInSelect() {
        return true;
    }

    public String[][] getIdentifierQuoteStrings() {
        return BasicSQLDialect.DEFAULT_IDENTIFIER_QUOTES;
    }

    @Override
    public boolean supportsAlterTableStatement() {
        return false;
    }

    @NotNull
    @Override
    public MultiValueInsertMode getDefaultMultiValueInsertMode()
    {
        return MultiValueInsertMode.GROUP_ROWS;
    }

    @Override
    public boolean supportsInsertAllDefaultValuesStatement() {
        return true;
    }

    @Override
    public boolean supportsIndexCreateAndDrop() {
        return true;
    }
}
