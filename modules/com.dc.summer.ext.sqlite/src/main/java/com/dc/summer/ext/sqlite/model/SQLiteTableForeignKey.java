
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.generic.model.GenericTableForeignKey;
import com.dc.summer.model.struct.DBSEntityReferrer;
import com.dc.summer.model.struct.rdb.DBSForeignKeyDeferability;
import com.dc.summer.model.struct.rdb.DBSForeignKeyModifyRule;
import com.dc.code.Nullable;

public class SQLiteTableForeignKey extends GenericTableForeignKey {
    public SQLiteTableForeignKey(GenericTableBase table, String name, @Nullable String remarks, DBSEntityReferrer referencedKey, DBSForeignKeyModifyRule deleteRule, DBSForeignKeyModifyRule updateRule, DBSForeignKeyDeferability deferability, boolean persisted) {
        super(table, name, remarks, referencedKey, deleteRule, updateRule, deferability, persisted);
    }
}
