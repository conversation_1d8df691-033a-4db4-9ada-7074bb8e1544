
package com.dc.summer.ext.sqlite.edit;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.edit.GenericForeignKeyManager;
import com.dc.summer.ext.generic.model.GenericTableForeignKey;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.sqlite.SQLiteUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class SQLiteTableForeignKeyManager extends GenericForeignKeyManager {
    @Override
    public boolean canCreateObject(Object container) {
        return true;
    }

    @Override
    public boolean canEditObject(GenericTableForeignKey object) {
        return true;
    }

    @Override
    public boolean canDeleteObject(GenericTableForeignKey object) {
        return true;
    }

    @Override
    protected void addObjectCreateActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectCreateCommand command, Map<String, Object> options) throws DBException {
        SQLiteUtils.createTableAlterActions(
            monitor,
            "Create foreign key " + DBUtils.getQuotedIdentifier(command.getObject()),
            command.getObject().getTable(),
            Collections.emptyList(),
            actions
        );
    }

    @Override
    protected void addObjectModifyActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectChangeCommand command, Map<String, Object> options) throws DBException {
        SQLiteUtils.createTableAlterActions(
            monitor,
            "Alter foreign key " + DBUtils.getQuotedIdentifier(command.getObject()),
            command.getObject().getTable(),
            Collections.emptyList(),
            actions
        );
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, ObjectDeleteCommand command, Map<String, Object> options) throws DBException {
        SQLiteUtils.createTableAlterActions(
            monitor,
            "Drop foreign key " + DBUtils.getQuotedIdentifier(command.getObject()),
            command.getObject().getTable(),
            Collections.emptyList(),
            actions
        );
    }
}
