
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.ext.generic.model.GenericTable;
import com.dc.summer.ext.generic.model.GenericUniqueKey;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.data.DBDPseudoAttribute;
import com.dc.summer.model.data.DBDPseudoAttributeContainer;
import com.dc.summer.model.data.DBDPseudoAttributeType;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.summer.model.struct.rdb.DBSTableConstraint;
import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.Collection;
import java.util.List;

public class SQLiteTable extends GenericTable implements DBDPseudoAttributeContainer, DBPNamedObject2 {

    private static final DBDPseudoAttribute PSEUDO_ATTR_ROWID = new DBDPseudoAttribute(
        DBDPseudoAttributeType.ROWID,
        "rowid",
        "$alias.rowid",
        null,
        "Unique row identifier",
        true);


    public SQLiteTable(GenericStructContainer container, @Nullable String tableName, @Nullable String tableType, @Nullable JDBCResultSet dbResult) {
        super(container, tableName, tableType, dbResult);
    }

    @Override
    protected boolean isTruncateSupported() {
        return false;
    }

    // We use ROWID only if we don't have primary key. Looks like it is the only way to determine ROWID column presence.
    @Override
    public DBDPseudoAttribute[] getPseudoAttributes() throws DBException {
        if (hasPrimaryKey()) {
            return null;
        }
        return new DBDPseudoAttribute[] { PSEUDO_ATTR_ROWID };
    }

    private boolean hasPrimaryKey() throws DBException {
        List<GenericUniqueKey> constraints = getConstraints(new VoidProgressMonitor());
        if (constraints != null) {
            for (DBSTableConstraint cons : constraints) {
                if (cons.getConstraintType() == DBSEntityConstraintType.PRIMARY_KEY) {
                    return true;
                }
            }
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    @Nullable
    @Override
    public List<SQLiteTableColumn> getAttributes(@NotNull DBRProgressMonitor monitor) throws DBException {
        return (List<SQLiteTableColumn>) super.getAttributes(monitor);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Collection<SQLiteTableForeignKey> getAssociations(@NotNull DBRProgressMonitor monitor) throws DBException {
        return (Collection<SQLiteTableForeignKey>) super.getAssociations(monitor);
    }

    @Override
    public SQLiteTableForeignKey getAssociation(@NotNull DBRProgressMonitor monitor, String name) throws DBException {
        return (SQLiteTableForeignKey) super.getAssociation(monitor, name);
    }
}
