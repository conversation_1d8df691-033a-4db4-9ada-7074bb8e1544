

package com.dc.summer.ext.sqlite;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.ext.sqlite.model.SQLiteObjectType;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.edit.SQLDatabasePersistActionComment;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.DBStructUtils;
import com.dc.code.NotNull;

import java.util.Collection;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * SQLiteUtils
 */
public class SQLiteUtils {

    private static final Log log = Log.getLog(SQLiteUtils.class);


    public static String readMasterDefinition(DBRProgressMonitor monitor, DBSObject sourceObject, SQLiteObjectType objectType, String sourceObjectName, GenericTableBase table) {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, sourceObject, "Load SQLite description")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                "SELECT sql FROM sqlite_master WHERE type=? AND tbl_name=?" + (sourceObjectName != null ? " AND name=?" : "") + "\n" +
                    "UNION ALL\n" +
                    "SELECT sql FROM sqlite_temp_master WHERE type=? AND tbl_name=?" + (sourceObjectName != null ? " AND name=?" : "") + "\n"))
            {
                int paramIndex = 1;
                dbStat.setString(paramIndex++, objectType.name());
                dbStat.setString(paramIndex++, table.getName());
                if (sourceObjectName != null) {
                    dbStat.setString(paramIndex++, sourceObjectName);
                }
                dbStat.setString(paramIndex++, objectType.name());
                dbStat.setString(paramIndex++, table.getName());
                if (sourceObjectName != null) {
                    dbStat.setString(paramIndex++, sourceObjectName);
                }
                try (JDBCResultSet resultSet = dbStat.executeQuery()) {
                    StringBuilder sql = new StringBuilder();
                    while (resultSet.next()) {
                        String ddl = resultSet.getString(1);
                        if (ddl != null) {
                            sql.append(ddl);
                            sql.append(";\n");
                        }
                    }
                    String ddl = sql.toString();
                    //ddl = ddl.replaceAll("(?i)CREATE VIEW", "CREATE OR REPLACE VIEW");
                    return ddl;
                }
            }
        } catch (Exception e) {
            log.debug(e);
            return null;
        }
    }

    public static void createTableAlterActions(@NotNull DBRProgressMonitor monitor, @NotNull String reason, @NotNull GenericTableBase table, @NotNull Collection<DBSAttributeBase> attributes, @NotNull Collection<DBEPersistAction> actions) throws DBException {
        final String columns = attributes.stream()
            .map(DBUtils::getQuotedIdentifier)
            .collect(Collectors.joining(",\n  "));

        actions.add(new SQLDatabasePersistActionComment(
            table.getDataSource(),
            reason
        ));
        actions.add(new SQLDatabasePersistAction(
            "Create temporary table from original table",
            "CREATE TEMPORARY TABLE temp AS\nSELECT"
                + (attributes.isEmpty() ? " *" : "\n  " + columns) + "\nFROM " + DBUtils.getQuotedIdentifier(table)
        ));
        actions.add(new SQLDatabasePersistAction(
            "Drop original table",
            "\nDROP TABLE " + DBUtils.getQuotedIdentifier(table) + ";\n"
        ));
        actions.add(new SQLDatabasePersistAction(
            "Create new table",
            DBStructUtils.generateTableDDL(monitor, table, Collections.emptyMap(), false)
        ));
        actions.add(new SQLDatabasePersistAction(
            "Insert values from temporary table to new table",
            "INSERT INTO " + DBUtils.getQuotedIdentifier(table)
                + (attributes.isEmpty() ? "" : "\n (" + columns + ")") + "\nSELECT"
                + (attributes.isEmpty() ? " *" : "\n  " + columns) + "\nFROM temp"
        ));
        actions.add(new SQLDatabasePersistAction(
            "Drop temporary table",
            "\nDROP TABLE temp"
        ));
    }
}
