
package com.dc.summer.ext.sqlite.model.data;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.data.DBDValueHandlerProvider;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCContentValueHandler;
import com.dc.utils.ArrayUtils;

/**
 * SQLiteValueHandlerProvider
 */
public class SQLiteValueHandlerProvider implements DBDValueHandlerProvider {

    @Nullable
    @Override
    public DBDValueHandler getValueHandler(DBPDataSource dataSource, DBDFormatSettings preferences, DBSTypedObject typedObject)
    {
        final DBPDataKind dataKind = typedObject.getDataKind();
        if (dataKind == DBPDataKind.BINARY) {
            return JDBCContentValueHandler.INSTANCE;
        }
        if (ArrayUtils.contains(SQLiteGeometryValueHandler.GEOMETRY_TYPES, typedObject.getTypeName())) {
            return SQLiteGeometryValueHandler.INSTANCE;
        }
        // All types must be handled by unified SQLite handler
        return new SQLiteValueHandler(typedObject, preferences);
    }

}