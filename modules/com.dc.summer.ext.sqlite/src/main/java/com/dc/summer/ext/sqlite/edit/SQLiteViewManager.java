
package com.dc.summer.ext.sqlite.edit;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.generic.edit.GenericViewManager;

import java.util.List;
import java.util.Map;

public class SQLiteViewManager extends GenericViewManager {
    @Override
    protected void addObjectModifyActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLObjectEditor<GenericTableBase, GenericStructContainer>.ObjectChangeCommand command, Map<String, Object> options) {
        if (!command.hasProperty(DBConstants.PROP_ID_DESCRIPTION) || command.getProperties().size() > 1) {
            actions.add(new SQLDatabasePersistAction(
                "Drop view",
                "DROP " + getDropViewType(command.getObject()) + " " + command.getObject().getFullyQualifiedName(DBPEvaluationContext.DDL)
            ));
        }
        super.addObjectModifyActions(monitor, executionContext, actions, command, options);
    }
}
