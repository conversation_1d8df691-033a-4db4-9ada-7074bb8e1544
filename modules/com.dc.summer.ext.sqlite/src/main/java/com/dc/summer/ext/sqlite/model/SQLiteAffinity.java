
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.model.DBPDataKind;

import java.sql.Types;

public enum SQLiteAffinity {

    INTEGER(DBPDataKind.NUMERIC, Types.BIGINT, 19, 0),
    REAL(DBPDataKind.NUMERIC, Types.DOUBLE, 17, 17),
    NUMERIC(DBPDataKind.NUMERIC, Types.NUMERIC, 17, 17),
    TEXT(DBPDataKind.STRING, Types.VARCHAR, Integer.MAX_VALUE, 0),
    BLOB(DBPDataKind.BINARY, Types.BINARY, Integer.MAX_VALUE, 0);

    private final DBPDataKind dataKind;
    private final int valueType;
    private final int precision;
    private final int scale;

    SQLiteAffinity(DBPDataKind dataKind, int valueType, int precision, int scale) {
        this.dataKind = dataKind;
        this.valueType = valueType;
        this.precision = precision;
        this.scale = scale;
    }

    public DBPDataKind getDataKind() {
        return dataKind;
    }

    public int getValueType() {
        return valueType;
    }

    public int getPrecision() {
        return precision;
    }

    public int getScale() {
        return scale;
    }
}
