
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.impl.jdbc.struct.JDBCDataType;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.meta.PropertyLength;

public class SQLiteDataType extends JDBCDataType<SQLiteDataSource> {

    private final SQLiteAffinity affinity;

    public SQLiteDataType(SQLiteDataSource sqLiteDataSource, SQLiteAffinity affinity) {
        super(sqLiteDataSource, affinity.getValueType(), affinity.name(), null, false, false, affinity.getPrecision(), 0, affinity.getScale());
        this.affinity = affinity;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return super.getName();
    }

    @Property(order = 10)
    public SQLiteAffinity getAffinity() {
        return affinity;
    }

    @Property(order = 20)
    @Override
    public DBPDataKind getDataKind() {
        return affinity.getDataKind();
    }

    @Nullable
    @Override
    @Property(viewable = true, length = PropertyLength.MULTILINE, order = 100)
    public String getDescription()
    {
        return super.getDescription();
    }

}
