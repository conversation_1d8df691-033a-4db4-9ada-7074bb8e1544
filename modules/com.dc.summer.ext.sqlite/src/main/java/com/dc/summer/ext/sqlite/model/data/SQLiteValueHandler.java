
package com.dc.summer.ext.sqlite.model.data;

import com.dc.summer.Log;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCAbstractValueHandler;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDDataFormatter;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.data.DBDValueHandlerConfigurable;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.impl.data.formatters.DefaultDataFormatter;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;
import java.util.Date;


/**
 * SQLiteValueHandler
 */
public class SQLiteValueHandler extends JDBCAbstractValueHandler implements DBDValueHandlerConfigurable {

    private static final Log log = Log.getLog(SQLiteValueHandler.class);

    private final DBDFormatSettings formatSettings;
    private final DBSTypedObject type;
    private DBDDataFormatter numberFormatter;
    private DBDDataFormatter timestampFormatter;

    public SQLiteValueHandler(DBSTypedObject type, DBDFormatSettings formatSettings)
    {
        this.formatSettings = formatSettings;
        this.type = type;
    }

    @Nullable
    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        Object object = resultSet.getObject(index);
        return getValueFromObject(session, type, object, false, false);
    }

    @Override
    protected void bindParameter(JDBCSession session, JDBCPreparedStatement statement, DBSTypedObject paramType, int paramIndex, Object value) throws DBCException, SQLException {
        statement.setObject(paramIndex, value);
    }

    @NotNull
    @Override
    public Class<?> getValueObjectType(@NotNull DBSTypedObject attribute) {
        return Object.class;
    }

    @Nullable
    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, @Nullable Object object, boolean copy, boolean validateValue) throws DBCException {
        return object;
    }

    @NotNull
    public synchronized String getValueDisplayString(@NotNull DBSTypedObject column, @Nullable Object value, @NotNull DBDDisplayFormat format)
    {
        if (value instanceof Number) {
            if (format == DBDDisplayFormat.NATIVE || format == DBDDisplayFormat.EDIT) {
                return DBValueFormatting.convertNumberToNativeString(
                    (Number) value,
                    formatSettings.isUseScientificNumericFormat());
            } else {
                if (numberFormatter == null) {
                    try {
                        numberFormatter = formatSettings.getDataFormatterProfile().createFormatter(DBDDataFormatter.TYPE_NAME_NUMBER, type);
                    } catch (Exception e) {
                        log.error("Can't create numberFormatter for number value handler", e); //$NON-NLS-1$
                        numberFormatter = DefaultDataFormatter.INSTANCE;
                    }
                }
                return numberFormatter.formatValue(value);
            }
        } else if (value instanceof Date) {

            if (timestampFormatter == null) {
                try {
                    timestampFormatter = formatSettings.getDataFormatterProfile().createFormatter(DBDDataFormatter.TYPE_NAME_TIMESTAMP, type);
                } catch (Exception e) {
                    log.error("Can't create timestampFormatter for timestamp value handler", e); //$NON-NLS-1$
                    timestampFormatter = DefaultDataFormatter.INSTANCE;
                }
            }

            return timestampFormatter.formatValue(value);
        }
        return super.getValueDisplayString(column, value, format);
    }

    @Override
    public void refreshValueHandlerConfiguration(DBSTypedObject type) {
        this.numberFormatter = null;
        this.timestampFormatter = null;
    }
}
