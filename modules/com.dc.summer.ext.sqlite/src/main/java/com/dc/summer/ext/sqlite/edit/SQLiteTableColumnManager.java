
package com.dc.summer.ext.sqlite.edit;

import com.dc.summer.DBException;
import com.dc.summer.ext.generic.edit.GenericTableColumnManager;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectRenamer;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.impl.sql.edit.SQLObjectEditor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.ext.generic.model.GenericTableColumn;
import com.dc.summer.ext.sqlite.SQLiteUtils;
import com.dc.summer.model.DBPPersistedObject;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SQLiteTableColumnManager
 */
public class SQLiteTableColumnManager extends GenericTableColumnManager implements DBEObjectRenamer<GenericTableColumn> {

    @Override
    public boolean canCreateObject(Object container) {
        return true;
    }

    @Override
    public boolean canDeleteObject(GenericTableColumn object) {
        return true;
    }

    @Override
    protected void addObjectDeleteActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLObjectEditor<GenericTableColumn, GenericTableBase>.ObjectDeleteCommand command, Map<String, Object> options) throws DBException {
        final GenericTableColumn column = command.getObject();
        final GenericTableBase table = column.getTable();

        final List<? extends GenericTableColumn> attributes = table.getAttributes(monitor);
        if (CommonUtils.isEmpty(attributes)) {
            throw new DBException("Table has no attributes");
        }
        try {
            if (attributes.contains(column)) {
                table.removeAttribute(column);
            }
            SQLiteUtils.createTableAlterActions(
                monitor,
                "Drop column " + DBUtils.getQuotedIdentifier(column),
                table,
                attributes.stream().filter(DBPPersistedObject::isPersisted).collect(Collectors.toList()),
                actions
            );
        } finally {
            if (attributes.contains(column)) {
                table.addAttribute(column);
            }
        }
    }

    @Override
    protected void addObjectRenameActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actions, SQLObjectEditor<GenericTableColumn, GenericTableBase>.ObjectRenameCommand command, Map<String, Object> options) {
        final GenericTableColumn column = command.getObject();

        actions.add(
            new SQLDatabasePersistAction(
                "Rename column",
                "ALTER TABLE " + DBUtils.getQuotedIdentifier(column.getTable()) + " RENAME COLUMN " +
                    DBUtils.getQuotedIdentifier(column.getDataSource(), command.getOldName()) +
                    " TO " + DBUtils.getQuotedIdentifier(column.getDataSource(), command.getNewName())));
    }

    @Override
    public void renameObject(@NotNull DBECommandContext commandContext, @NotNull GenericTableColumn object, @NotNull Map<String, Object> options, @NotNull String newName) throws DBException {
        processObjectRename(commandContext, object, options, newName);
    }

}
