
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.impl.DBDummyNumberTransformer;
import com.dc.summer.model.meta.Property;
import com.dc.summer.ext.generic.model.GenericTableColumn;

public class SQLiteTableColumn extends GenericTableColumn implements DBPNamedObject2 {
    public SQLiteTableColumn(GenericTableBase table, String columnName, String typeName, int valueType, int sourceType, int ordinalPosition, long columnSize, long charLength, Integer scale, Integer precision, int radix, boolean notNull, String remarks, String defaultValue, boolean autoIncrement, boolean autoGenerated) {
        super(table, columnName, typeName, valueType, sourceType, ordinalPosition, columnSize, charLength, scale, precision, radix, notNull, remarks, defaultValue, autoIncrement, autoGenerated);
    }

    // Not a property
    @Override
    public boolean isAutoGenerated() {
        return super.isAutoGenerated();
    }

    @Property(viewable = true, editable = true, order = 52, editableExpr = "!object.table.persisted")
    @Override
    public boolean isAutoIncrement() {
        return super.isAutoIncrement();
    }

    @Property(viewable = true, editable = true, order = 40, valueRenderer = DBDummyNumberTransformer.class)
    @Override
    public long getMaxLength() {
        return super.getMaxLength();
    }

    // Not a property
    @Override
    public Integer getScale() {
        return super.getScale();
    }

    // Not a property
    @Override
    public Integer getPrecision() {
        return super.getPrecision();
    }
}
