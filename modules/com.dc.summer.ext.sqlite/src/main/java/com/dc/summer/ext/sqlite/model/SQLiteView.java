
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.code.Nullable;
import com.dc.summer.ext.generic.model.GenericView;
import com.dc.summer.model.DBPNamedObject2;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;

public class SQLiteView extends GenericView implements DBPNamedObject2 {

    public SQLiteView(GenericStructContainer container, @Nullable String tableName, @Nullable String tableType, @Nullable JDBCResultSet dbResult) {
        super(container, tableName, tableType, dbResult);
    }

    @Override
    protected boolean isTruncateSupported() {
        return false;
    }
}
