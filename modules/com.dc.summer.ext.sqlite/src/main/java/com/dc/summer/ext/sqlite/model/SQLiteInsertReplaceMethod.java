
package com.dc.summer.ext.sqlite.model;

import com.dc.code.NotNull;
import com.dc.summer.model.data.DBDInsertReplaceMethod;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.rdb.DBSTable;

public class SQLiteInsertReplaceMethod implements DBDInsertReplaceMethod {

    @NotNull
    @Override
    public String getOpeningClause(DBSTable table, DBRProgressMonitor monitor) {
        return "INSERT OR REPLACE INTO";
    }

    @Override
    public String getTrailingClause(DBSTable table, DBRProgressMonitor monitor, DBSAttributeBase[] attributes) {
        return null;
    }
}
