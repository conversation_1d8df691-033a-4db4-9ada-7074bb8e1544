
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.ext.generic.model.GenericStructContainer;
import com.dc.summer.DBException;
import com.dc.summer.model.impl.jdbc.cache.JDBCBasicDataTypeCache;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.ArrayList;
import java.util.List;

/**
 * SQLiteDataTypeCache
 */
public class SQLiteDataTypeCache extends JDBCBasicDataTypeCache<GenericStructContainer, SQLiteDataType>
{

    public SQLiteDataTypeCache(GenericStructContainer owner) {
        super(owner);
    }

    @Override
    protected synchronized void loadObjects(DBRProgressMonitor monitor, GenericStructContainer dataSource) throws DBException {
        List<SQLiteDataType> types = new ArrayList<>();
        for (SQLiteAffinity affinity : SQLiteAffinity.values()) {
            SQLiteDataType dataType = new SQLiteDataType((SQLiteDataSource) dataSource, affinity);
            types.add(dataType);
        }
        setCache(types);
    }
}
