
package com.dc.summer.ext.sqlite.model;

import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.GenericDataSourceInfo;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSObject;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class SQLiteDataSource extends GenericDataSource {

    public SQLiteDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, GenericMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new SQLiteSQLDialect());
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, @NotNull JDBCDatabaseMetaData metaData) {
        GenericDataSourceInfo info = (GenericDataSourceInfo) super.createDataSourceInfo(monitor, metaData);
        info.setSupportsNullableUniqueConstraints(true);
        return info;
    }

    @Override
    protected boolean isPopulateClientAppName() {
        return false;
    }

    @Override
    public DBSDataType getLocalDataType(String typeName) {
        // Resolve type name according to https://www.sqlite.org/datatype3.html
        typeName = typeName.toUpperCase(Locale.ENGLISH);
        SQLiteAffinity affinity;
        if (typeName.startsWith("INT")) {
            affinity = SQLiteAffinity.INTEGER;
        } else if (typeName.contains("CHAR") || typeName.contains("CLOB") || typeName.contains("TEXT") || typeName.startsWith("DATE") || typeName.startsWith("TIME")) {
            affinity = SQLiteAffinity.TEXT;
        } else if (typeName.contains("BLOB")) {
            affinity = SQLiteAffinity.BLOB;
        } else if (typeName.startsWith("REAL") || typeName.startsWith("FLOA") || typeName.startsWith("DOUB")) {
            affinity = SQLiteAffinity.REAL;
        } else {
            affinity = SQLiteAffinity.NUMERIC;
        }
        return super.getLocalDataType(affinity.name());
    }

    @Override
    protected boolean isConnectionReadOnlyBroken() {
        return true;
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        Map<String, String> connectionsProps = new HashMap<>();
        if (getContainer().isConnectionReadOnly()) {
            // Read-only prop
            connectionsProps.put("open_mode", "1");  //1 == readonly
        }
        connectionsProps.put("enable_load_extension", String.valueOf(true));
        return connectionsProps;
    }

    @NotNull
    @Override
    public Class<? extends DBSObject> getPrimaryChildType(@Nullable DBRProgressMonitor monitor) throws DBException {
        return SQLiteTable.class;
    }

    @Override
    public ErrorType discoverErrorType(@NotNull Throwable error) {
        if (error instanceof SQLException && ((SQLException) error).getErrorCode() == 19) {
            // SQLite doesn't have a specific Exception SQL state for the constraint violation, but it has a specific vendor code
            return ErrorType.UNIQUE_KEY_VIOLATION;
        }
        return super.discoverErrorType(error);
    }
}
