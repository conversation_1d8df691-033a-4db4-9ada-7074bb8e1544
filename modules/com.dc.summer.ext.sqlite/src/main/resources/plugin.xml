<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="com.dc.summer.generic.meta">
        <meta id="sqlite" class="com.dc.summer.ext.sqlite.model.SQLiteMetaModel" driverClass="org.sqlite.JDBC" dialect="sqlite"/>
    </extension>

    <extension point="com.dc.summer.dataSourceProvider">
        <datasource
                class="com.dc.summer.ext.sqlite.model.SQLiteDataSourceProvider"
                description="SQLite"
                id="sqlite"
                parent="generic"
                label="SQLite"
                icon="platform:/plugin/com.dc.summer.ext.generic/icons/sqlite_icon.png"
                dialect="sqlite">
            <drivers managable="true">
                <driver
                        id="sqlite_jdbc"
                        label="SQLite"
                        icon="platform:/plugin/com.dc.summer.ext.generic/icons/sqlite_icon.png"
                        iconBig="platform:/plugin/com.dc.summer.ext.generic/icons/sqlite_icon_big.png"
                        class="org.sqlite.JDBC"
                        sampleURL="jdbc:sqlite:{file}"
                        defaultPort=""
                        webURL="https://github.com/xerial/sqlite-jdbc"
                        propertiesURL="https://www.sqlite.org/pragma.html"
                        embedded="true"
                        anonymous="true"
                        description="SQLite JDBC driver"
                        promoted="1"
                        categories="sql,embedded">
                    <replace provider="generic" driver="sqlite_zentus"/>
                    <replace provider="generic" driver="sqlite_xerial"/>
                    <replace provider="generic" driver="sqlite_ch_werner"/>
                    <replace provider="generic" driver="sqlite_jdbc"/>

                    <file type="jar" path="maven:/org.xerial:sqlite-jdbc:********" bundle="!drivers.sqlite.xerial"/>
                    <file type="license" path="drivers/sqlite/xerial/LICENSE.txt" bundle="drivers.sqlite.xerial"/>
                    <file type="jar" path="drivers/sqlite/xerial" bundle="drivers.sqlite.xerial"/>

                    <parameter name="supports-references" value="true"/>
                    <parameter name="supports-struct-cache" value="false"/>
                    <parameter name="supports-indexes" value="true"/>
                    <parameter name="supports-stored-code" value="false"/>
                    <parameter name="supports-truncate" value="false"/>
                </driver>
            </drivers>
        </datasource>
    </extension>

    <extension point="com.dc.summer.objectManager">
        <manager class="com.dc.summer.ext.sqlite.edit.SQLiteTableManager" objectType="com.dc.summer.ext.sqlite.model.SQLiteTable"/>
        <manager class="com.dc.summer.ext.sqlite.edit.SQLiteTableColumnManager" objectType="com.dc.summer.ext.sqlite.model.SQLiteTableColumn"/>
        <manager class="com.dc.summer.ext.sqlite.edit.SQLiteTableForeignKeyManager" objectType="com.dc.summer.ext.sqlite.model.SQLiteTableForeignKey"/>
        <manager class="com.dc.summer.ext.sqlite.edit.SQLiteViewManager" objectType="com.dc.summer.ext.sqlite.model.SQLiteView"/>
    </extension>

    <extension point="com.dc.summer.dataTypeProvider">
        <provider
                class="com.dc.summer.ext.sqlite.model.data.SQLiteValueHandlerProvider"
                description="SQLite data types provider"
                id="com.dc.summer.ext.sqlite.model.data.SQLiteValueHandlerProvider"
                label="SQLite data types provider">

            <datasource class="com.dc.summer.ext.sqlite.model.SQLiteDataSource"/>
            <type name="*"/>
        </provider>
    </extension>

    <extension point="com.dc.summer.sqlInsertMethod">
        <method id="sqLiteInsertIgnore" class="com.dc.summer.ext.sqlite.model.SQLiteInsertReplaceMethodIgnore" label="INSERT OR IGNORE" description="Insert ignore duplicate key value"/>
        <method id="sqLiteReplaceIgnore" class="com.dc.summer.ext.sqlite.model.SQLiteInsertReplaceMethod" label="INSERT OR REPLACE" description="Insert replace duplicate key value"/>
    </extension>

    <extension point="com.dc.summer.sqlDialect">
        <dialect id="sqlite" parent="generic" class="com.dc.summer.ext.sqlite.model.SQLiteSQLDialect" label="SQLite" description="SQLite dialect." icon="platform:/plugin/com.dc.summer.ext.generic/icons/sqlite_icon.png">
            <property name="insertMethods" value="sqLiteInsertIgnore,sqLiteReplaceIgnore"/>
        </dialect>
    </extension>

</plugin>
