### 描述
本模块使用手写的monitor和executeContext管理。

### 处理程序
#### 数据源连接容器 - [DataSourceConnectionHandler.java](src%2Fmain%2Fjava%2Fcom%2Fdc%2Fsummer%2Fexecution%2Fhandler%2FDataSourceConnectionHandler.java)

- 全局使用这个容器进行SQL执行，主要负责连接的生命周期管理，包含打开连接、最大上下文数量、定时销毁、数据源更新、数据源刷新、关闭连接、日志埋点等功能。
- 使用方式如下：
- 1. 获取数据源 `public static DataSourceConnectionHandler handle(...)`
- 2. 获取连接 `public JDBCExecutionContext getExecutionContext(...)` 
- 3. 执行 `DBExecUtils.execute(...)`
- 4. 查询 `DBExecUtils.executeQuery(...)`
- 5. 更新 `DBExecUtils.executeUpdate(...）`

#### 默认数据源容器 - [DefaultDataSourceHandler.java](src%2Fmain%2Fjava%2Fcom%2Fdc%2Fsummer%2Fexecution%2Fhandler%2FDefaultDataSourceHandler.java)

- 为了拆分SQL，提供的临时数据源容，主要目的是不初始化数据库详细信息，但是要加载驱动信息。

#### 测试数据源连接容器 - [TestDataSourceConnectionHandler.java](src%2Fmain%2Fjava%2Fcom%2Fdc%2Fsummer%2Fexecution%2Fhandler%2FTestDataSourceConnectionHandler.java)

- 为了进行连接测试，提供一个临时的、用完销毁的、与主容器隔离的数据源容器，不会进行具体数据库信息的加载。

#### 分析器 - [ParserHandler.java](src%2Fmain%2Fjava%2Fcom%2Fdc%2Fsummer%2Fexecution%2Fhandler%2FParserHandler.java)

- 根据生成的容器，获取具体的方言。

#### 记录程序 - [RecordHandler.java](src%2Fmain%2Fjava%2Fcom%2Fdc%2Fsummer%2Fexecution%2Fhandler%2FRecordHandler.java)

- 与本地文件进行读写操作，用来记录日志、记录监控数据。

#### SQL语句处理

- 负责记录每个 statement 的信息，用户指令调用中断操作。

### 配置文件
#### 容器配置 - [HandlerConfig.java](src%2Fmain%2Fjava%2Fcom%2Fdc%2Fsummer%2Fexecution%2Fconfig%2FHandlerConfig.java)
- 通过属性确定，是否需要打印日志和强关连接。

#### 连接配置 - [SessionConfig.java](src%2Fmain%2Fjava%2Fcom%2Fdc%2Fsummer%2Fexecution%2Fconfig%2FSessionConfig.java)
- 主要配置超时时间、存活时间、最大数量限制。