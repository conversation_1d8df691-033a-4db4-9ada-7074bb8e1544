package com.dc.summer.exec.monitor.impl;

import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.model.ConnectionManager;
import com.dc.summer.exec.model.data.ConnectionConfiguration;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.exec.monitor.AbstractCacheStatMonitor;
import com.dc.summer.exec.monitor.data.DataSourceStatResult;
import com.dc.summer.exec.monitor.data.StatParam;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.utils.ArithmeticUtils;
import com.dc.utils.NotNullUtils;
import com.dc.utils.bean.CloneUtils;
import com.dc.utils.bean.ReflectUtils;
import org.osgi.framework.Version;

import java.util.*;
import java.util.stream.Collectors;

public class DataSourceMonitor extends AbstractCacheStatMonitor<DataSourceStatResult> {

    @Override
    public String getName() {
        return "stat.datasource";
    }

    @Override
    protected void execute() {

        final Map<String, ConnectionManager> connectionManagerMap = CloneUtils.transListToMap(
                new ArrayList<>(DataSourceConnectionHandler.getFake().getAllConnectionManagers()),
                DBPDataSourceContainer::getId,
                connectionManager -> connectionManager);

        Map<String, DataSourceStatResult> resultMap = CloneUtils.transListToMap(super.read(),
                cord -> ((DataSourceStatResult) cord.getRow()).getContainerId(), cord -> (DataSourceStatResult) cord.getRow());

        Iterator<Map.Entry<String, DataSourceStatResult>> iterator = resultMap.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, DataSourceStatResult> entry = iterator.next();
            DataSourceStatResult value = entry.getValue();
            String containerId = value.getContainerId();
            ConnectionManager connectionManager = connectionManagerMap.get(containerId);
            if (connectionManager == null) {
                iterator.remove();
            }
        }

        RecordHandler handler = RecordHandler.handle(RecordType.DATASOURCE);
        List<RecordHandler.Record> list = handler.extractRow(StatParam.class);
        super.cachesToList(list);

        list.stream()
                .filter(cord -> cord.getRow() != null && ((StatParam) cord.getRow()).getContainerId() != null)
                .collect(Collectors.groupingBy(cord -> ((StatParam) cord.getRow()).getContainerId()))
                .values()
                .forEach(records -> {

                    RecordHandler.Record start = null;
                    RecordHandler.Record get = null;

                    for (RecordHandler.Record cord : records) {
                        StatParam param = (StatParam) cord.getRow();
                        String containerId = param.getContainerId();
                        if (containerId == null) {
                            list.remove(cord);
                            continue;
                        }
                        resultMap.putIfAbsent(containerId, new DataSourceStatResult());
                        DataSourceStatResult result = resultMap.get(containerId);

                        switch (cord.getSign()) {
                            case CONTAINER_HANDLE: {
                                ConnectionManager connectionManager = connectionManagerMap.get(containerId);
                                DataSourceConnectionHandler container = (DataSourceConnectionHandler) connectionManager;
                                ConnectionConfiguration connectionConfiguration = (ConnectionConfiguration) container.getConnectionConfiguration();
                                DBPDriver driver = container.getDriver();

                                result.setContainerId(containerId);
                                result.setName(container.getName());
                                result.setUsername(connectionConfiguration.getUserName());
                                result.setUrl(NotNullUtils.get(connectionConfiguration.getUrl(), driver.getSampleURL()));
                                result.setDbType(connectionConfiguration.getDatabaseType().name());
                                result.setDriverClassName(driver.getDriverClassName());
                                result.setInitializeConnection(!new Version(0, 0, 0).equals(connectionManager.getDataSource().getInfo().getDatabaseVersion()));
                                result.setHandleCount(container.getCount());
                                result.setAutoCloseTransactions(container.isAutoCloseTransactions());
                                result.setDefaultAutoCommit(container.isDefaultAutoCommit());
                                result.setConnectionReadOnly(container.isConnectionReadOnly());
                                result.setForceUseSingleConnection(container.isForceUseSingleConnection());
                                result.setTemporary(container.isTemporary());
                                result.setHidden(container.isHidden());
                                result.setConnected(container.isConnected());
                                result.setDefaultTransactionsIsolation(container.getDefaultTransactionsIsolation());
                                result.setActiveTransactionsIsolation(container.getActiveTransactionsIsolation().getCode());
                                result.setModifyPermission(container.getModifyPermission());
                                result.setConnectTime(container.getConnectTime());
                                result.setAccessCheckRequired(container.isAccessCheckRequired());
                                result.setManageable(container.isManageable());
                                result.setTemplate(container.isTemplate());
                                result.setDriverId(connectionConfiguration.getDriverId());
                                result.setKeepAliveInterval(connectionConfiguration.getKeepAliveInterval());
                                result.setCloseIdleInterval(connectionConfiguration.getCloseIdleInterval());
                                result.setDatabaseName(connectionConfiguration.getDatabaseName());
                                result.setNumberOfConnectionsInThePool(container.getAllContexts().size());
                                if (result.getNumberOfConnectionsInThePool() > result.getPeakNumberOfConnectionsInThePool()) {
                                    result.setPeakNumberOfConnectionsInThePool(result.getNumberOfConnectionsInThePool());
                                    result.setPeakTimeOfConnectionsInThePool(new Date());
                                }
                                list.remove(cord);
                                break;
                            }
                            case EXECUTE_START:
                                start = cord;
                                Date date = new Date();
                                /*
                                以下字段赋值:
                                     executeCount
                                     executeQueryCount
                                     executeUpdateCount
                                     executeBatchCount
                                 */
                                String fieldName = param.getMethodName()
                                        .replace("Statement", "")
                                        .replace("Large", "") + "Count";
                                Long value = ReflectUtils.getField(result, fieldName, Long.class);
                                ReflectUtils.setField(result, fieldName, ArithmeticUtils.plus(value));
                                result.setNumberOfActiveConnections(ArithmeticUtils.plus(result.getNumberOfActiveConnections()));
                                result.setNumberOfTransactionStartups(ArithmeticUtils.plus(result.getNumberOfTransactionStartups()));
                                if (result.getNumberOfActiveConnections() > result.getPeakNumberOfActiveConnections()) {
                                    result.setPeakNumberOfActiveConnections(result.getNumberOfActiveConnections());
                                    result.setPeakTimeOfActiveConnections(date);
                                }
                                break;
                            case EXECUTE_OVER:
                                result.setNumberOfActiveConnections(ArithmeticUtils.minus(result.getNumberOfActiveConnections()));
                                list.remove(start);
                                list.remove(cord);
                                break;
                            case EXECUTE_COMMIT:
                                result.setNumberOfTransactionStartups(ArithmeticUtils.minus(result.getNumberOfTransactionStartups()));
                                result.setCommitCount(ArithmeticUtils.plus(result.getCommitCount()));
                                if (start != null) {
                                    result.setTransactionTimePart(fill(result.getTransactionTimePart(), cord.getTime() - start.getTime()));
                                }
                                list.remove(cord);
                                break;
                            case EXECUTE_ROLLBACK:
                                result.setNumberOfTransactionStartups(ArithmeticUtils.minus(result.getNumberOfTransactionStartups()));
                                result.setRollbackCount(ArithmeticUtils.plus(result.getRollbackCount()));
                                if (start != null) {
                                    result.setTransactionTimePart(fill(result.getTransactionTimePart(), cord.getTime() - start.getTime()));
                                }
                                list.remove(cord);
                                break;
                            case EXECUTE_ERROR:
                                result.setErrorCount(ArithmeticUtils.plus(result.getErrorCount()));
                                result.setNumberOfActiveConnections(ArithmeticUtils.minus(result.getNumberOfActiveConnections()));
                                list.remove(start);
                                list.remove(cord);
                                break;
                            case CONTEXT_ADD:
                                result.setConnectOpenCount(ArithmeticUtils.plus(result.getConnectOpenCount()));
                                list.remove(cord);
                                break;
                            case CONTEXT_CLOSE:
                                result.setConnectCloseCount(ArithmeticUtils.plus(result.getConnectCloseCount()));
                                list.remove(cord);
                                break;
                            case CONTEXT_ERROR:
                                result.setConnectErrorCount(ArithmeticUtils.plus(result.getConnectErrorCount()));
                                list.remove(cord);
                                break;
                            case CONTEXT_GET:
                                if (get != null) {
                                    result.setConnectHoldTimePart(fill(result.getConnectHoldTimePart(), cord.getTime() - get.getTime()));
                                    list.remove(get);
                                }
                                get = cord;
                                break;
                            case CONTEXT_OVER:
                                if (get != null) {
                                    result.setConnectHoldTimePart(fill(result.getConnectHoldTimePart(), cord.getTime() - get.getTime()));
                                    list.remove(get);
                                    list.remove(cord);
                                }
                                break;
                            default:
                        }
                    }
                });


        super.listToCaches(list);
        super.write(resultMap.values());
    }

    @Override
    protected RecordType getRecordType() {
        return RecordType.STAT_DATASOURCE;
    }

}
