package com.dc.summer.exec.monitor;

import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.monitor.data.MonitorResult;
import com.dc.summer.exec.monitor.data.MonitorParam;
import com.dc.summer.exec.model.observer.MonitorSubject;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.model.DBPNamedObject;
import com.dc.utils.ArithmeticUtils;
import com.dc.utils.BeanUtils;
import com.dc.utils.formatter.HumpFormatter;
import com.dc.utils.io.FindClassUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractStatMonitor<T> implements DBPNamedObject, Runnable, Function<MonitorParam, MonitorResult> {

    private static volatile Set<AbstractStatMonitor<?>> monitorSet;

    private static volatile Map<String, AbstractStatMonitor<?>> monitorMap;

    @Getter
    private Date runTime = new Date();
    private volatile boolean init = true;

    public static Set<AbstractStatMonitor<?>> getMonitorSet() {
        if (monitorSet == null) {
            synchronized (AbstractStatMonitor.class) {
                if (monitorSet == null) {
                    loadMonitor();
                }
            }
        }
        return monitorSet;
    }


    public static Map<String, AbstractStatMonitor<?>> getMonitorMap() {
        if (monitorMap == null) {
            synchronized (AbstractStatMonitor.class) {
                if (monitorMap == null) {
                    loadMonitor();
                }
            }
        }
        return monitorMap;
    }

    private static void loadMonitor() {
        monitorSet = FindClassUtils
                .getAchieveClass(AbstractStatMonitor.class, AbstractStatMonitor.class.getPackage().getName())
                .stream()
                .map(clazz -> {
                    try {
                        return (AbstractStatMonitor<?>) clazz.getDeclaredConstructor().newInstance();
                    } catch (NoSuchMethodException | InvocationTargetException | InstantiationException |
                             IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toSet());
        monitorMap = new HashMap<>();
        for (AbstractStatMonitor<?> abstractStatMonitor : monitorSet) {
            monitorMap.put(abstractStatMonitor.getName(), abstractStatMonitor);
        }
    }


    protected static long[] fill(long[] part, Long lump) {
        int digit = lump == 0 ? 0 : (int) Math.log10(lump);
        if (part.length > digit) {
            part[digit] = ArithmeticUtils.plus(part[digit]);
        }
        return part;
    }

    protected abstract void execute();

    protected MonitorResult collect(MonitorParam monitorParam) {
        List<Object> collect = read().stream().map(RecordHandler.Record::getRow).collect(Collectors.toList());
        return new MonitorResult().setResult(collection(monitorParam, collect));
    }

    protected abstract RecordType getRecordType();

    @SuppressWarnings("unchecked")
    private Class<T> getResultClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    protected List<RecordHandler.Record> read() {
        RecordHandler handle = RecordHandler.handle(getRecordType());
        return handle.readRow(getResultClass());
    }

    protected void write(Collection<T> rows) {
        RecordHandler handle = RecordHandler.handle(getRecordType());
        handle.writeRow(rows.toArray());
    }

    protected void init() {
        init = false;
        write(new ArrayList<>());
    }

    /**
     * 守护线程调用
     */
    @Override
    public void run() {
        exec();
    }

    /**
     * 监控手动调用
     */
    @Override
    public MonitorResult apply(MonitorParam monitorParam) {
        if (Boolean.TRUE.equals(monitorParam.getTimely())) {
            exec();
            this.runTime = new Date();
        }
        return collect(monitorParam).setRunTime(runTime).setParam(monitorParam);
    }

    private void exec() {
        if (init) {
            init();
        }
        execute();
    }

    /**
     * redis 调用
     */
    public void run(Date runTime) {
        exec();
        this.runTime = runTime;
        MonitorSubject.transit(this, monitorParam ->
                collect(monitorParam).setRunTime(this.runTime).setParam(monitorParam));
    }

    protected Collection<?> collection(MonitorParam monitorParam, Collection<?> results) {

        final String keywords = monitorParam.getKeywords();
        final String condition = monitorParam.getCondition();

        if (StringUtils.isNotBlank(keywords) || StringUtils.isNotBlank(condition)) {

            results = results.stream().filter(result -> {

                boolean match = true;

                Map<String, Object> allField = BeanUtils.getAllFieldValue(result, true);

                if (StringUtils.isNotBlank(keywords)) {
                    match = match && allField.values().stream().filter(Objects::nonNull).anyMatch(value -> value.toString().contains(keywords));
                }

                if (StringUtils.isNotBlank(condition)) {
                    match = match && Arrays.stream(condition.split(","))
                            .allMatch(term -> {

                                String[] symbol = new String[]{"-", "~", "<", ">"};
                                for (String s : symbol) {
                                    if (term.contains(s)) {
                                        String[] split = term.split(s);
                                        String name = HumpFormatter.formatUnderLine(split[0]);
                                        String value = split[1];
                                        Object o = allField.get(name);

                                        if (o == null) {
                                            return false;
                                        }

                                        if ("~".equals(s)) {
                                            return o.toString().contains(value);
                                        }

                                        int compare;

                                        if (o instanceof Integer) {
                                            compare = ((Integer) o).compareTo(Integer.valueOf(value));
                                        } else if (o instanceof Long) {
                                            compare = ((Long) o).compareTo(Long.valueOf(value));
                                        } else if (o instanceof Boolean) {
                                            compare = ((Boolean) o).compareTo(Boolean.valueOf(value));
                                        } else if (o instanceof Date) {
                                            compare = Long.compare(((Date) o).getTime(), Long.parseLong(value));
                                        } else {
                                            compare = o.toString().compareTo(value);
                                        }

                                        boolean eq = compare == 0 && "-".equals(s);
                                        boolean lt = compare < 0 && "<".equals(s);
                                        boolean gt = compare > 0 && ">".equals(s);

                                        return eq || lt || gt;
                                    }
                                }
                                return true;
                            });
                }

                return match;

            }).collect(Collectors.toList());

        }

        return results;
    }

}
