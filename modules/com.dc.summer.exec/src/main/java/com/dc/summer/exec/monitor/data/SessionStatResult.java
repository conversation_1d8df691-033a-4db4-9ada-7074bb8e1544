package com.dc.summer.exec.monitor.data;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class SessionStatResult {

    private String containerId;

    private String connectionId;

    private String sessionId;

    private Date createDate;

    private Date lastDate;

    private Integer requestCount;

    private Long requestTime;

    private boolean ongoing;

    private Integer jdbcExecuteCount;

    private Long jdbcExecuteTime;

    private Integer transactionCommit;

    private Integer transactionRollback;

    private Long readRowCount;

    private Long renewRowCount;

    private String operator;

    private Integer dbType;

    private String node;

    private String currSql;

    private boolean hasTransaction;

    private String containerName;

    private String connectionDesc;

    private String instanceName;

    private Integer environment;

    private String userId;

    private Integer connectionPattern;

    public void setUserId(String userId) {
        if (userId != null) {
            this.userId = userId;
        }
    }

    public void setOperator(String operator) {
        if (operator != null) {
            this.operator = operator;
        }
    }

    public void setConnectionPattern(Integer connectionPattern) {
        if (connectionPattern != null) {
            this.connectionPattern = connectionPattern;
        }
    }

    public String getCurrSql() {
        return StringUtils.isBlank(currSql) ? "--" : currSql;
    }

}
