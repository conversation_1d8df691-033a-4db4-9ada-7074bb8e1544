package com.dc.summer.exec.monitor;

import com.dc.summer.exec.handler.RecordHandler;

import java.util.List;

public abstract class AbstractCacheStatMonitor<T> extends AbstractStatMonitor<T> {

    private List<RecordHandler.Record> caches;

    protected void cachesToList(List<RecordHandler.Record> list) {
        if (caches != null && !caches.isEmpty()) {
            list.addAll(caches);
            caches = null;
        }
    }

    protected void listToCaches(List<RecordHandler.Record> list) {
        caches = list;
    }

}
