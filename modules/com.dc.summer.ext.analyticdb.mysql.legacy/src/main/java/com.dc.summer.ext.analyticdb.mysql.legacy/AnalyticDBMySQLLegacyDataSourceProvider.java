package com.dc.summer.ext.analyticdb.mysql.legacy;

import com.dc.summer.DBException;
import com.dc.summer.ext.analyticdb.mysql.legacy.model.AnalyticDBMySQLLegacyDataSource;
import com.dc.summer.ext.mysql.MySQLDataSourceProvider;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class AnalyticDBMySQLLegacyDataSourceProvider extends MySQLDataSourceProvider {

    @Override
    public DBPDataSource openDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new AnalyticDBMySQLLegacyDataSource(monitor, container);
    }

}
