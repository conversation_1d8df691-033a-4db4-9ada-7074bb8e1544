package com.dc.summer.ext.analyticdb.mysql.legacy.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.model.MySQLDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class AnalyticDBMySQLLegacyDataSource extends MySQLDataSource {
    public AnalyticDBMySQLLegacyDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        super(monitor, container);
    }

    @Override
    protected DBPDataSourceInfo createDataSourceInfo(DBRProgressMonitor monitor, JDBCDatabaseMetaData metaData) {
        return new AnalyticDBMySQLLegacyDataSourceInfo(metaData);
    }

    @Override
    protected JDBCExecutionContext createExecutionContext(JDBCRemoteInstance instance, String type) {
        return new AnalyticDBMySQLLegacyExecutionContext(instance, type);
    }
}
