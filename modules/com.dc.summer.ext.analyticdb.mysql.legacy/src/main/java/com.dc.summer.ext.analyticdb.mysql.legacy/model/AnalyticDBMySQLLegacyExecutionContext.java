package com.dc.summer.ext.analyticdb.mysql.legacy.model;

import com.dc.code.NotNull;
import com.dc.summer.ext.mysql.model.MySQLExecutionContext;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.jdbc.JDBCRemoteInstance;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public class AnalyticDBMySQLLegacyExecutionContext extends MySQLExecutionContext {

    AnalyticDBMySQLLegacyExecutionContext(@NotNull JDBCRemoteInstance instance, String purpose) {
        super(instance, purpose);
    }

    @Override
    protected boolean setCurrentDatabaseName(DBRProgressMonitor monitor, String databaseName) throws DBCException {
        return false;
    }
}
