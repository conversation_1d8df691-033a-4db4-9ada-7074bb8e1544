package com.dc.summer.ext.analyticdb.mysql.legacy.model;

import com.dc.summer.DBException;
import com.dc.summer.ext.mysql.model.MySQLDataSourceInfo;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.utils.NewJdbcUtil;
import com.dc.utils.BitTypeUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class AnalyticDBMySQLLegacyDataSourceInfo extends MySQLDataSourceInfo {
    public AnalyticDBMySQLLegacyDataSourceInfo(JDBCDatabaseMetaData metaData) {
        super(metaData);
    }

    @Override
    public boolean isQueryStatementLockTable() {
        return false;
    }

    @Override
    public List<Map<String, Object>> getSchemasInfo(String userName, DBRProgressMonitor monitor, DBCExecutionContext context, Function<String, DBCExecutionContext> dbNameContextFunc) throws DBException {
        List<Map<String, Object>> list = DBExecUtils.executeQuery(monitor, context, "get schema list",
                "select CHARACTER_SET_NAME, A.TABLE_GROUP as \"SCHEMA_NAME\",B.COUNT as \"COUNT\" \n" +
                        "from information_schema.table_groups A  \n" +
                        "LEFT JOIN (select TABLE_GROUP,count(*) as COUNT from information_schema.tables group by table_group) B \n" +
                        "ON A.TABLE_GROUP=B.TABLE_GROUP");

        ArrayList<Map<String, Object>> returnList = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> returnMap = new LinkedHashMap<>();
            returnMap.put("addLabel", "dc_adb_mysql_db_schema");
            returnMap.put("username", map.get("SCHEMA_NAME"));
            returnMap.put("charset", map.get("CHARACTER_SET_NAME") != null ? map.get("CHARACTER_SET_NAME").toString() : map.get("character_set_name") != null ? map.get("character_set_name").toString() : "");

            returnMap.put("is_sys", 0);

            returnMap.put("count", 0L);
            if (map.get("COUNT") != null) {
                returnMap.put("count", Long.parseLong(map.get("COUNT").toString()));
            }

            returnList.add(returnMap);
        }

        return returnList;
    }

    @Override
    public String generateInsertSql(String schemaName, String tableName, List<SqlFieldData> list, String content) {
        if (list == null) {
            return "";
        }
        List<String> fields = list.stream().map(SqlFieldData::getFieldName).collect(Collectors.toList());
        List<String> data = new ArrayList<>();
        for (SqlFieldData item : list) {
            if (null != item.getFieldValue() && StringUtils.isNotBlank(item.getFieldValue().toString())) {
                if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("VARCHAR", "NVARCHAR", "CHAR").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String varcharData = item.getFieldValue().toString().contains("\\") ? item.getFieldValue().toString().replace("\\\\", "\\\\\\\\") : item.getFieldValue().toString();
                    varcharData = varcharData.contains("\"") ? varcharData.replace("\"", "\\\"") : varcharData;
                    varcharData = varcharData.contains("'") ? varcharData.replace("'", "''") : varcharData;
                    data.add(String.format("'%s'", varcharData));
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && "BIT".equals(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String value;
                    String fieldValue = item.getFieldValue().toString();

                    if (BitTypeUtils.isValidPattern(fieldValue)) {
                        value = fieldValue;
                    } else {
                        value = String.format("b'%s'", fieldValue);
                    }
                    data.add(value);
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("BINARY", "VARBINARY").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    String hex = NewJdbcUtil.bytes2HexString(NewJdbcUtil.string2Bytes(item.getFieldValue().toString()));
                    data.add(String.format("0x%s", hex));
                } else if (StringUtils.isNotBlank(item.getFieldValue().toString()) && Arrays.asList("GEOMETRY", "GEOMETRYCOLLECTION", "MULTIPOINT", "MULTIPOLYGON", "POLYGON", "LINESTRING", "MULTILINESTRING", "POINT").contains(item.getFieldType().toUpperCase(Locale.ROOT))) {
                    data.add(String.format("ST_GeomFromText('%s')", item.getFieldValue()));
                } else {
                    String varcharData = item.getFieldValue().toString().contains("\"") ? item.getFieldValue().toString().replaceAll("\"", "\\\"") : item.getFieldValue().toString();
                    data.add(String.format("'%s'", varcharData));
                }
            } else {
                data.add(String.format("%s", "NULL"));
            }
        }
        String columns = StringUtils.join(fields, "`,`");
        String values = StringUtils.join(data, ",");

        return String.format("INSERT INTO `%s` (%s) VALUES (%s)", tableName, "`" + columns + "`", values);
    }

    @Override
    public String generateTruncateSql(String schemaName, String tableName) {

        return String.format("TRUNCATE TABLE `%s`", tableName);
    }
}
